{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[6423159271834319385, "build_script_build", false, 1546978502630628846], [17031207854228184515, "build_script_build", false, 4833849940976976259], [10755362358622467486, "build_script_build", false, 10408035954228330508], [371826037427084582, "build_script_build", false, 953143414524966218], [3834743577069889284, "build_script_build", false, 6926916923142396019], [1582828171158827377, "build_script_build", false, 11351958072431279205]], "local": [{"RerunIfChanged": {"output": "debug\\build\\Oway-9c3b6d6f999d53a6\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}