<template>
  <!-- <CMSetupModal v-if="showSetupModal" @close="showSetupModal = false" /> -->

  <div class="relative flex flex-col w-1/3 h-full overflow-hidden bg-primary px-1 pb-1">
    <div class="flex justify-between items-center relative text-sm font-semibold px-2 py-2 gap-0.5">
      <h2 class="select-none text-base-800">Organized Category</h2>
      <div
        class="relative flex flex-col text-primary-700 hover:text-primary-800 cursor-pointer transition-all duration-200"
        @click="toggleSortState"
        title="Sort by unread emails"
      >
        <i v-if="sortState === 'none'" class="pi pi-align-left size-4"></i>
        <i v-if="sortState === 'desc'" class="pi pi-sort-amount-down size-4"></i>
        <i v-if="sortState === 'asc'" class="pi pi-sort-amount-down-alt size-4"></i>
      </div>
    </div>

    <div class="w-full custom-scrollbar mb-2 overflow-y-auto overflow-x-hidden px-2">
      <ul class="w-full flex flex-col gap-2">
        <h3 class="select-none text-base-800">Todays</h3>
        <li
          v-for="tocategory in activeTodayCategories"
          :key="tocategory.id"
          @click="currentSessionStore.selectEmailCategory(tocategory.category_id, tocategory)"
          class="h-14 flex justify-start items-center gap-1 p-1 drop-shadow-sm rounded text-base-900 cursor-pointer transition-colors duration-150"
          :class="{
            'border-l-4 border-l-secondary-400 bg-secondary-200  hover:bg-secondary-200/60':
              currentSessionStore.currentEmailCategory?.id === tocategory.id,
            ' bg-primary-600/10 hover:bg-primary-600/20':
              currentSessionStore.currentEmailCategory?.id !== tocategory.id,
            'bg-whitex': currentSessionStore.currentEmailCategory?.id !== tocategory.id,
            'border-[category.color]': tocategory.color || 'border-gray-300',
          }"
        >
          <div class="w-10 h-full flex justify-center items-center">
            <TagIcon v-if="!tocategory.name" class="size-6 text-base-800" />
            <div
              v-if="tocategory.name"
              class="size-9 flex justify-center items-center rounded-full bg-primary-600/20 text-primary-800"
              :class="{
                'bg-secondary-300 text-secondary-900': currentSessionStore.currentEmailCategory?.id === tocategory.id,
              }"
            >
              {{ abbrName(tocategory.name) }}
            </div>
          </div>
          <div>
            <div class="text-sm font-semibold">
              {{ tocategory.name || "Unknown Company" }}
              <!-- ({{ category.sender_domain || "Unknown Domain" }}) -->
            </div>

            <!-- Description -->
            <!-- <div v-if="category.description" class="text-sm text-gray-600 mb-1">
              {{ category.description }}
            </div> -->

            <!-- Tags -->
            <!-- <div v-if="category.tags" class="text-xs text-[#008080] mb-2">
        Tags: {{ category.tags.split(",").join(", ") }}
      </div> -->

            <!-- Priority -->
            <div v-if="tocategory.priority" class="text-xs">
              Priority:
              <span
                :class="{
                  'text-red-600': tocategory.priority <= 2,
                  'text-orange-500': tocategory.priority > 2 && tocategory.priority <= 5,
                  'text-green-500': tocategory.priority > 5,
                }"
                >{{ tocategory.priority }}</span
              >
            </div>

            <!-- Unread Count and Latest Email Time -->
            <div class="text-[10px]">
              <span v-if="tocategory.unread_count">Unread Emails: {{ tocategory.unread_count }} |</span>
              <span v-if="tocategory.latest_email_at">
                Latest Email: {{ dayjs.utc(tocategory.latest_email_at).local().fromNow() }}
              </span>
            </div>

            <!-- Last Accessed Time -->
            <!-- <div v-if="category.last_accessed_at" class="text-xs text-[#4B0082]">
        Last Accessed: {{ dayjs.utc(category.last_accessed_at).local().fromNow() }}
      </div> -->

            <!-- Created At -->
            <!-- <div class="text-xs text-[#4B0082]">Created: {{ dayjs.utc(category.created_at).local().fromNow() }}</div> -->

            <!-- Parent Category Indicator -->
            <!-- <div v-if="category.parent_category_id" class="text-xs text-gray-500 mt-2">
        <i class="fas fa-layer-group mr-1"></i> Sub-category of {{ category.parent_category_id }}
      </div> -->

            <!-- Visibility and Sync Status -->
            <div class="flex space-x-2">
              <!-- <div v-if="category.visibility" class="text-xs text-gray-500">
          <i class="fas fa-eye mr-1"></i> Visibility: {{ category.visibility }}
        </div> -->
              <!-- <div v-if="category.is_synced" class="text-xs text-green-500">
          <i class="fas fa-sync-alt mr-1"></i> Synced
        </div> -->
            </div>

            <!-- Custom Data -->
            <!-- <div v-if="category.custom_data" class="text-xs text-gray-500 mt-2">
        Custom Data: {{ category.custom_data }}
      </div> -->

            <!-- Archived Status -->
            <!-- <div v-if="category.is_archived" class="text-red-500 text-sm mt-2">Archived</div> -->
          </div>
        </li>
        <details class="relative w-full group">
          <summary
            class="cursor-pointer px-4 py-2 bg-primary-600/10 rounded-md flex justify-between items-center text-sm font-semibold hover:bg-primary-600/20"
          >
            Select Email Category
            <svg
              class="w-4 h-4 ml-2 group-open:rotate-180 transition-transform"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </summary>
          <ul class="w-full flex flex-col gap-2">
            <li
              v-for="category in sessions"
              :key="category.id"
              @click="currentSessionStore.selectEmailCategory(category.category_id, category)"
              class="h-14 flex justify-start items-center gap-1 p-1 drop-shadow-sm rounded text-base-900 cursor-pointer transition-colors duration-150"
              :class="{
                'border-l-4 border-l-secondary-400 bg-secondary-200  hover:bg-secondary-200/60':
                  currentSessionStore.currentEmailCategory?.id === category.id,
                ' bg-primary-600/10 hover:bg-primary-600/20':
                  currentSessionStore.currentEmailCategory?.id !== category.id,
                'bg-whitex': currentSessionStore.currentEmailCategory?.id !== category.id,
                'border-[category.color]': category.color || 'border-gray-300',
              }"
            >
              <div class="w-10 h-full flex justify-center items-center">
                <TagIcon v-if="!category.sender_company" class="size-6 text-base-800" />
                <div
                  v-if="category.sender_company"
                  class="size-9 flex justify-center items-center rounded-full bg-primary-600/20 text-primary-800"
                  :class="{
                    'bg-secondary-300 text-secondary-900': currentSessionStore.currentEmailCategory?.id === category.id,
                  }"
                >
                  {{ abbrName(category.sender_company) }}
                </div>
              </div>
              <div>
                <div class="text-sm font-semibold">
                  {{ category.sender_company || "Unknown Company" }}
                  <!-- ({{ category.sender_domain || "Unknown Domain" }}) -->
                </div>

                <!-- Priority -->
                <div v-if="category.priority" class="text-xs">
                  Priority:
                  <span
                    :class="{
                      'text-red-600': category.priority <= 2,
                      'text-orange-500': category.priority > 2 && category.priority <= 5,
                      'text-green-500': category.priority > 5,
                    }"
                    >{{ category.priority }}</span
                  >
                </div>

                <!-- Unread Count and Latest Email Time -->
                <div class="text-[10px]">
                  <span v-if="category.unread_count">Unread Emails: {{ category.unread_count }} |</span>
                  <span v-if="category.latest_email_at">
                    Latest Email: {{ dayjs.utc(category.latest_email_at).local().fromNow() }}
                  </span>
                </div>

                <div class="flex space-x-2"></div>
              </div>
            </li>
          </ul>
        </details>
      </ul>
      <hr style="border: none; height: 1px; background-color: #ccc" />
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import utc from "dayjs/plugin/utc";
import { onMounted, ref, watch } from "vue";
import { EmailCategory } from "../../types";
import { invoke } from "@tauri-apps/api/core";
import { listen } from "@tauri-apps/api/event";
import { useCurrentAssistantStore } from "../../stores/currentAssistant";
import { useCurrentEmailCategoryStore } from "../../stores/currentSession";
import { useCurrentUserStore } from "../../stores/currentUser";
import { TagIcon } from "@heroicons/vue/24/outline";
import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/vue/20/solid";
import { User, EmailContext, PhoneCallContext } from "../../types";
import CMSetupModal from "./CMSetupModal.vue";
import { emit } from "@tauri-apps/api/event";

dayjs.extend(relativeTime);
dayjs.extend(utc);

const currentAssistantStore = useCurrentAssistantStore();
const currentSessionStore = useCurrentEmailCategoryStore();
const currentUserStore = useCurrentUserStore();
// currentUserStore.init();
const sessions = ref<EmailCategory[]>([]);
const activeTodayCategories = ref<EmailCategory[]>([]);
const todaycategories = ref<EmailCategory[]>([]);
const showSetupModal = ref(true);

const emailCategories = ref<EmailCategory[]>([]);
const categories = ref<EmailCategory[]>([]);

const sortState = ref<"none" | "asc" | "desc">("none");

function toggleSortState() {
  switch (sortState.value) {
    case "none":
      sortState.value = "desc";
      break;
    case "desc":
      sortState.value = "asc";
      break;
    case "asc":
      sortState.value = "none";
      break;
  }
  // sort();
}

watch(sortState, (value) => {
  //  console.log("Sorting =>", value);
  sort(value);
});

function sort(value: "asc" | "desc" | "none") {
  const values = sessions.value;
  function desc(a: number, b: number) {
    return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;
  }

  function asc(a: number, b: number) {
    return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;
  }
  switch (value) {
    case "none":
      sessions.value = categories.value;
      break;
    case "asc":
      values.sort((a, b) => asc(a.unread_count ?? 0, b.unread_count ?? 0));
      sessions.value = values;
      break;
    case "desc":
      values.sort((a, b) => desc(a.unread_count ?? 0, b.unread_count ?? 0));
      sessions.value = values;
      break;
  }
}

async function fetchCategories() {
  try {
    emailCategories.value = await listSessions();
    categories.value = emailCategories.value;

    //  console.log("CurrentUserStore after initialization:", currentUserStore.currentEmailContext);
  } catch (error) {
    console.error("Error fetching email categories:", error);
  }
}

async function fetchAllEmailsAndUnread() {
  try {
    // Fetch all emails first
    const allEmailsResponse = await invoke("fetch_all_emails", {
      message: "fetch_emails",
      tone: "none",
      keyPoints: "none",
    });
    //  console.log("All emails:", allEmailsResponse);
    // After successfully fetching all emails, proceed to fetch unread emails
    // const unreadEmailsResponse = await invoke("fetch_unread_emails");
    // console.log("Unread emails:", unreadEmailsResponse);
  } catch (err) {
    console.error("Failed to fetch emails:", err);
  }
}

function abbrName(name: string) {
  let nameArray = name.replace(/\s+/g, " ").split(" ");
  let letters = nameArray.map((word) => (word[0] ? word[0].toUpperCase() : ""));
  return `${letters[0]}${letters.length > 1 ? letters[1] ?? "" : ""}`;
}

onMounted(async () => {
  // if (currentUserStore.currentUser && currentUserStore.currentUser.email) {
  //   // Email exists, proceed with your logic
  // }
  // emit("notification", {
  //   title: "Success!",
  //   body: "Your changes have been saved.",
  //   type: "success", // Must match a key in typeOptions
  // });
  console.log("CurrentUserStore after initialization:", currentUserStore);
  listSessionsToday();
  if (currentUserStore.currentUser) {
    //  console.log("CurrentUserStore after initialization:", currentUserStore.currentUser.email);
  } else {
    console.error("currentUser is undefined after initialization");
  }

  fetchAllEmailsAndUnread();
  emailCategories.value = await listSessions();
  // setInterval(fetchCategories, 5000);

  if (currentSessionStore.currentEmailCategory?.id) {
    sessions.value = await listSessions();
  }

  if (!currentSessionStore.currentEmailCategory?.id) {
    selectFirstSession();
  }

  await listen("session_updated", async () => {
    sessions.value = await listSessions();
  });

  await listen("session_deleted", async () => {
    sessions.value = await listSessions();

    selectFirstSession();
  });

  // await listen("email_category_deleted", async () => {
  //   emailCategories.value = await listSessions();

  //   selectFirstSession();
  // });

  categories.value = emailCategories.value;
  //  console.log("#### CATEGORIES ", categories.value);
  //  console.log("Categories", emailCategories.value);
  //  console.log(sessions.value);
});

currentAssistantStore.$subscribe(async (_, state) => {
  if (!state.currentAssistant?.id) {
    return;
  }

  sessions.value = await listSessions();
  //  console.log("Sessions after assistant change:", sessions.value);
  selectFirstSession();
});

async function listSessions(): Promise<EmailCategory[]> {
  return invoke("list_email_categories");
}

async function listSessionsToday() {
  activeTodayCategories.value = await invoke("get_today_categories");
  //  console.log("Active Today Categories:", activeTodayCategories.value);
}

function selectFirstSession() {
  if (sessions.value.length > 0) {
    currentSessionStore.selectEmailCategory(sessions.value[0].category_id, sessions.value[0]);
  }
}

// async function filterTodayCategories() {
//   try {
//     const todaysCategoryIds: string[] = await invoke("get_today_categories");
//     //  console.log("✅ Server responded with categories:", todaysCategoryIds);

//     activeTodayCategories.value = todaycategories.value.filter((cat) => todaysCategoryIds.includes(cat.category_id));

//     //  console.log("🎯 Filtered today categories:", activeTodayCategories.value);
//   } catch (error) {
//     console.error("❌ Failed to fetch today's categories:", error);
//   }
// }
</script>
<style scoped>
.custom-bg {
  background-color: #f5f5dc;
}
.custom-teal {
  background-color: #274a53;
}
.custom-green {
  background-color: #3b4f4b;
}
</style>
