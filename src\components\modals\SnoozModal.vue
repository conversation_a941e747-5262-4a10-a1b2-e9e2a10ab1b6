<template>
  <!-- <PERSON><PERSON> Backdrop -->
  <div v-if="isVisible" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50" @click="closeModal">
    <!-- Modal Content -->
    <div class="bg-primary rounded-lg shadow-xl w-96 max-w-[90vw] p-6" @click.stop>
      <!-- <PERSON><PERSON>er -->
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-dark-600">Snooze Email</h2>
        <button
          @click="closeModal"
          class="text-secondary-600 hover:text-red-500 transition-colors duration-200 rounded-md size-8 flex justify-center items-center"
        >
          <XMarkIcon class="size-5" />
        </button>
      </div>

      <!-- Warning for unsupported categories -->
      <div v-if="!canSnoozeThisEmail" class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
        <div class="flex items-start gap-3">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
          <div>
            <h3 class="text-sm font-medium text-red-800">Cannot Snooze This Email</h3>
            <p class="text-sm text-red-700 mt-1">
              This email category "{{ emailCategory || "Unknown" }}" does not support snoozing.
            </p>
            <p class="text-xs text-red-600 mt-2">Only these categories can be snoozed: {{ supportedCategoriesText }}</p>
          </div>
        </div>
      </div>

      <!-- Quick Time Options -->
      <div v-if="canSnoozeThisEmail" class="mb-6">
        <h3 class="text-sm font-medium text-dark-600 mb-3">Quick Options</h3>
        <div class="grid grid-cols-2 gap-3">
          <button
            v-for="option in quickOptions"
            :key="option.value"
            @click="selectQuickOption(option)"
            :class="[
              'p-3 rounded-lg border-2 transition-all duration-200 text-left',
              selectedOption?.value === option.value
                ? 'border-secondary-500 bg-secondary-100 text-secondary-700'
                : 'border-primary-300 bg-primary-200 hover:border-secondary-300 hover:bg-secondary-50 text-dark-600',
            ]"
          >
            <div class="flex items-center gap-2">
              <component :is="option.icon" class="size-4" />
              <span class="font-medium">{{ option.label }}</span>
            </div>
            <div class="text-xs text-secondary-600 mt-1">{{ option.description }}</div>
          </button>
        </div>
      </div>

      <!-- Custom Time Section -->
      <div v-if="canSnoozeThisEmail" class="mb-6">
        <h3 class="text-sm font-medium text-dark-600 mb-3">Custom Time</h3>
        <button
          @click="toggleCustomTime"
          :class="[
            'w-full p-3 rounded-lg border-2 transition-all duration-200 text-left',
            showCustomTime
              ? 'border-secondary-500 bg-secondary-100 text-secondary-700'
              : 'border-primary-300 bg-primary-200 hover:border-secondary-300 hover:bg-secondary-50 text-dark-600',
          ]"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <ClockIcon class="size-4" />
              <span class="font-medium">Custom</span>
            </div>
            <ChevronDownIcon
              :class="['size-4 transition-transform duration-200', showCustomTime ? 'rotate-180' : '']"
            />
          </div>
          <div class="text-xs text-secondary-600 mt-1">Set a specific date and time</div>
        </button>

        <!-- Custom Time Inputs -->
        <div v-if="showCustomTime" class="mt-4 p-4 bg-primary-100 rounded-lg border border-primary-300">
          <div class="grid grid-cols-2 gap-4">
            <!-- Date Input -->
            <div>
              <label class="block text-xs font-medium text-dark-600 mb-2">Date</label>
              <input
                type="date"
                v-model="customDate"
                :min="minDate"
                class="w-full px-3 py-2 bg-primary border border-primary-400 rounded-md text-dark-600 focus:outline-none focus:ring-2 focus:ring-secondary-400 focus:border-transparent"
              />
            </div>

            <!-- Time Input -->
            <div>
              <label class="block text-xs font-medium text-dark-600 mb-2">Time</label>
              <input
                type="time"
                v-model="customTime"
                class="w-full px-3 py-2 bg-primary border border-primary-400 rounded-md text-dark-600 focus:outline-none focus:ring-2 focus:ring-secondary-400 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Selected Time Preview -->
      <div v-if="selectedDateTime" class="mb-6 p-3 bg-accent-100 rounded-lg border border-accent-300">
        <div class="text-sm text-accent-700">
          <span class="font-medium">Snooze until:</span>
          {{ formatSelectedDateTime }}
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex gap-3">
        <button
          @click="closeModal"
          class="flex-1 px-4 py-2 bg-primary-300 text-dark-600 rounded-lg hover:bg-primary-400 transition-colors duration-200"
        >
          Cancel
        </button>
        <button
          @click="confirmSnooze"
          :disabled="!selectedDateTime || !canSnoozeThisEmail"
          :class="[
            'flex-1 px-4 py-2 rounded-lg transition-colors duration-200',
            selectedDateTime && canSnoozeThisEmail
              ? 'bg-secondary-500 text-white hover:bg-secondary-600'
              : 'bg-primary-400 text-secondary-400 cursor-not-allowed',
          ]"
        >
          Snooze Email
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { XMarkIcon, ClockIcon, ChevronDownIcon, SunIcon, CalendarDaysIcon } from "@heroicons/vue/24/outline";
import { canSnoozeCategory, SNOOZABLE_CATEGORIES, snoozeEmail } from "../../commands/snooze";
import { toast } from "vue-sonner";
import { useSnoozedEmails } from "../../composables/useSnoozedEmails";

// Props
const props = defineProps<{
  modelValue: boolean;
  threadId?: string;
  emailCategory?: string;
}>();

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
}>();

// Reactive state
const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const showCustomTime = ref(false);
const selectedOption = ref<QuickOption | null>(null);
const customDate = ref("");
const customTime = ref("");

// Types
interface QuickOption {
  label: string;
  value: string;
  description: string;
  icon: any;
  hours: number;
}

// Quick time options
const quickOptions: QuickOption[] = [
  {
    label: "1 Hour",
    value: "1h",
    description: "Snooze for 1 hour",
    icon: ClockIcon,
    hours: 1,
  },
  {
    label: "5 Hours",
    value: "5h",
    description: "Snooze for 5 hours",
    icon: SunIcon,
    hours: 5,
  },
  {
    label: "1 Day",
    value: "1d",
    description: "Snooze until tomorrow",
    icon: CalendarDaysIcon,
    hours: 24,
  },
  {
    label: "3 Days",
    value: "3d",
    description: "Snooze for 3 days",
    icon: CalendarDaysIcon,
    hours: 72,
  },
  {
    label: "1 Week",
    value: "1w",
    description: "Snooze for 1 week",
    icon: CalendarDaysIcon,
    hours: 168,
  },
];

// Computed properties
const minDate = computed(() => {
  const today = new Date();
  return today.toISOString().split("T")[0];
});

const selectedDateTime = computed(() => {
  if (selectedOption.value) {
    const now = new Date();
    return new Date(now.getTime() + selectedOption.value.hours * 60 * 60 * 1000);
  } else if (showCustomTime.value && customDate.value && customTime.value) {
    return new Date(`${customDate.value}T${customTime.value}`);
  }
  return null;
});

const formatSelectedDateTime = computed(() => {
  if (!selectedDateTime.value) return "";

  const options: Intl.DateTimeFormatOptions = {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  };

  return selectedDateTime.value.toLocaleDateString("en-US", options);
});

// Check if the email category supports snoozing
const canSnoozeThisEmail = computed(() => {
  return canSnoozeCategory(props.emailCategory);
});

// Get list of supported categories for display
const supportedCategoriesText = computed(() => {
  return SNOOZABLE_CATEGORIES.join(", ");
});

// Use the snoozed emails composable to refresh after snoozing
const { refreshSnoozedEmails } = useSnoozedEmails();

// Methods
const closeModal = () => {
  isVisible.value = false;
  resetForm();
};

const resetForm = () => {
  selectedOption.value = null;
  showCustomTime.value = false;
  customDate.value = "";
  customTime.value = "";
};

const selectQuickOption = (option: QuickOption) => {
  selectedOption.value = option;
  showCustomTime.value = false;
};

const toggleCustomTime = () => {
  showCustomTime.value = !showCustomTime.value;
  if (showCustomTime.value) {
    selectedOption.value = null;
    // Set default custom time to 1 hour from now
    const now = new Date();
    const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);
    customDate.value = oneHourLater.toISOString().split("T")[0];
    customTime.value = oneHourLater.toTimeString().slice(0, 5);
  }
};

const confirmSnooze = async () => {
  if (!selectedDateTime.value || !props.threadId) return;

  if (!canSnoozeThisEmail.value) {
    toast.error("This email category does not support snoozing");
    return;
  }

  try {
    console.log(`Snoozing thread ${props.threadId} until ${selectedDateTime.value.toISOString()}`);

    // Call the snooze command with thread ID
    const result = await snoozeEmail(props.threadId, selectedDateTime.value, props.emailCategory);

    console.log("Thread snoozed successfully:", result);

    // Show success toast
    toast.success("Email snoozed successfully!", {
      description: `Will reappear on ${formatSelectedDateTime.value}`,
    });

    // Refresh snoozed emails to update the UI
    await refreshSnoozedEmails();

    // Close the modal
    closeModal();
  } catch (error) {
    console.error("Failed to snooze thread:", error);
    toast.error("Failed to snooze email", {
      description: error instanceof Error ? error.message : "An unexpected error occurred",
    });
  }
};

// Watch for modal visibility changes to reset form
watch(isVisible, (newValue) => {
  if (!newValue) {
    resetForm();
  }
});
</script>

<style scoped></style>
