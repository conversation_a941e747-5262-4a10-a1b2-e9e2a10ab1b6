<template>
  <div class="fixed inset-0 bg-black/50 flex items-center justify-center py-6 px-14">
    <button
      @click="emit('goPrevious')"
      class="absolute left-1 top-1/2 z-50 bg-secondary-100/60 -translate-y-1/2 p-1 rounded-md hover:bg-secondary-100 transition-colors duration-150"
    >
      <ChevronLeftIcon class="size-8" />
    </button>
    <div class="bg-primary w-full h-full rounded-md shadow-lg overflow-hidden flex flex-col relative">
      <div class="h-10 bg-primary-200 flex justify-between items-center pl-3">
        <div class="flex gap-4 w-32">
          <button
            title="Archive"
            @click="archiveEmail"
            class="text-gray-500 hover:text-dark-400 transition-colors duration-200"
          >
            <ArchiveBoxIcon class="w-5 h-5" />
          </button>
          <button
            title="Mark as unread"
            @click="markAsUnread"
            class="text-gray-500 hover:text-dark-400 transition-colors duration-200"
          >
            <EnvelopeIcon class="w-5 h-5" />
          </button>
          <button
            title="Snooze"
            @click="openSnoozeModal"
            class="text-gray-500 hover:text-dark-400 transition-colors duration-200"
          >
            <ClockIcon class="w-5 h-5" />
          </button>
          <button
            title="Delete Email"
            @click="deleteEmail"
            class="text-gray-500 hover:text-red-400 transition-colors duration-200"
          >
            <TrashIcon class="w-5 h-5" />
          </button>
        </div>

        <div class="flex flex-1 justify-center items-center h-10">
          <div class="h-full peer group flex flex-colx w-full justify-between items-center cursor-pointer">
            <div class="w-24"></div>
            <h2
              :title="email?.subject"
              class="text-xl font-bold text-dark-500 text-center text-ellipsis line-clamp-1 leading-x3"
            >
              {{ email?.subject.replace(/"/g, "") }}
            </h2>
            <div class="flex text-xs w-24">
              <!-- {{ new Date(email.date).toDateString() }} -->
              <div class="text-[10px] text-dark-500 mt-1 font-semibold">
                <!-- {{ new Date(email.date).toDateString() }} • From:
                <p class="group-hover:hidden inline">{{ email.from.split("<")[0] }}</p>
                <p class="hidden group-hover:inline">{{ extractEmail(email.from) }}</p> -->
                <!-- </div> -->
              </div>
            </div>
          </div>
          <div
            v-if="email?.cc || email?.bcc"
            class="h-0 py-0 peer-hover:h-auto hover:h-auto overflow-hidden transition-all duration-300 flex-col justify-center rounded-b-lg items-center absolute top-10 bg-primary-600/20 min-w-44 px-2 peer-hover:py-1"
          >
            <div class="flex flex-col gap-1 w-full">
              <div v-if="email?.cc || true" class="w-full flex justify-around gap-2 text-sm text-gray-500">
                <div class="font-bold underline-offset-2 underline">Cc:</div>
                <div class="flex flex-col font-semibold gap-1">
                  <div v-if="email.cc" v-for="c of JSON.parse(email?.cc)">{{ c }}</div>
                </div>
              </div>
              <div v-if="email?.bcc || true" class="w-full flex justify-around gap-2 text-sm text-gray-500">
                <div class="font-bold underline-offset-2 underline">Bcc:</div>
                <div class="flex flex-col font-semibold gap-1">
                  <div v-if="email.bcc" v-for="bc of JSON.parse(email?.bcc)">{{ bc }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Close Button -->
        <div class="w-24 flex justify-end">
          <button @click="$emit('close')" class="p-2 text-gray-500 hover:text-red-600">
            <XMarkIcon class="size-6" />
          </button>
        </div>
      </div>

      <div class="w-full h-[calc(100%-40px)] flex relative">
        <div class="flex-1 h-full flex flex-col">
          <!-- Email Header Section -->
          <!-- <div class="flex flex-col w-full justify-center items-center p-2 px-20">
            <h2 class="text-2xl font-bold text-dark-500 text-center">{{ email.subject.replace(/"/g, "") }}</h2>
            <div class="text-sm text-gray-500 mt-1">
              {{ new Date(email.date).toDateString() }} • From: {{ email.from }}
            </div>
            <div v-if="email.cc" class="text-sm text-gray-500">Cc: {{ email.cc }}</div>
          </div> -->

          <!-- Email Information Section -->
          <!-- <div class="flex items-start mb-6 flex-none">
          <div>
            <div class="text-lg font-semibold text-gray-900">{{ email.fromName }}</div>
            <div class="text-sm text-gray-500">{{ email.fromEmail }}</div>
          </div>
        </div> -->
          <!-- Email Body -->
          <div
            :key="fetchTry"
            @scroll="onScroll"
            class="text-gray-700 p-2 whitespace-pre-wrap leading-relaxed flex-grow overflow-y-auto mb-4 custom-scrollbar relative"
            id="html-container"
          >
            <div class="flex flex-col space-y-4 p-4">
              <div v-for="(msg, index) in emailMessages" :key="msg.id || index" class="flex items-start space-x-2">
                <!-- Abbreviation bubble -->
                <div
                  class="w-10 h-10 rounded-full bg-secondary-300 text-dark-600 text-xs flex items-center justify-center"
                  :title="msg.from"
                >
                  {{ fullNameAbbr(msg.from) }}
                </div>
                <!-- Message content -->
                <div class="flex-1 shadow-md rounded-xl bg-secondary-200 w-full">
                  <!-- Sender & Recipients -->
                  <div class="text-[12px] text-gray-600 mb-1">
                    <!-- Sender Line + Toggle Button -->
                    <div class="flex items-center gap-2">
                      <span class="capitalize font-semibold text-sm pt-1 pl-2">{{ getNameOnly(msg.from) }}</span>
                      <!-- Toggle Button -->
                      <button
                        @click="toggleField(index, 'details')"
                        class="text-xs hover:font-semibold hover:underline"
                      >
                        {{ showFields[index]?.details ? "Hide details" : "Show details" }}
                      </button>
                      <div class="ml-auto mr-2 text-xs text-gray-500">{{ formatDate(msg.date) }}</div>
                    </div>
                    <!-- Full Details: shown only when toggled -->
                    <div v-if="showFields[index]?.details" class="mt-1 space-y-0.5">
                      <div>
                        <span class="underline mr-2">From:</span>
                        <span class="text-sm">
                          {{ getEmailOnly(msg.from) }}
                        </span>
                      </div>
                      <div v-if="msg.to">
                        <span class="underline mr-2">To:</span>
                        <span @click="toggleField(index, 'to')" class="text-sm hover:cursor-pointer">
                          {{ getDisplayName(msg.to, index, "to") }}
                        </span>
                      </div>
                      <div v-if="msg.cc">
                        <span class="underline mr-2">Cc:</span>
                        <span @click="toggleField(index, 'cc')" class="text-sm hover:cursor-pointer">
                          {{ getDisplayName(msg.cc, index, "cc") }}
                        </span>
                      </div>
                      <div v-if="msg.bcc">
                        <span class="underline mr-2">Bcc:</span>
                        <span @click="toggleField(index, 'bcc')" class="text-sm hover:cursor-pointer">
                          {{ getDisplayName(msg.bcc, index, "bcc") }}
                        </span>
                      </div>
                    </div>
                  </div>
                  <!-- Message body -->
                  <div
                    class="pb-2 pt-2 flex flex-col items-start text-black pl-3 rounded-xl text-sm"
                    v-html="msg.body"
                  ></div>
                </div>
              </div>
            </div>

            <div v-if="draft" class="px-10 bg-primary-300 rounded-md py-5">
              <div class="flex justify-center items-center gap-1 text-secondary-600 text-2xl">
                <PaperClipIcon class="size-6" />
                <p>Draft</p>
              </div>
              <div v-html="draft.body"></div>
              <div class="flex justify-center items-center gap-4">
                <button
                  @click="()=>sendDraft(draft!)"
                  class="px-6 py-1 bg-secondary-400 rounded text-white hover:bg-secondary-500 transition-colors duration-200"
                >
                  Send
                </button>
                <button
                  @click="()=>refuseDraft(draft!)"
                  class="p-1.5 text-red-500 bg-red-50 underline hover:text-red-50 hover:bg-red-500 transition-colors duration-200 rounded"
                >
                  <TrashIcon class="size-5" />
                </button>
              </div>
            </div>
            <button
              v-if="showGoToBottom && draft"
              @click="GoToBottom"
              class="size-10 flex justify-center items-center bg-blue-500 text-blue-100 fixed bottom-28 right-28 rounded-full"
            >
              <PaperClipIcon class="size-5" />
            </button>
          </div>

          <!-- Action Buttons -->
          <div class="w-full flex items-center justify-between px-4 py-2">
            <div class="flex gap-2">
              <button
                @click="replyEmail"
                class="flex items-center gap-2 bg-primary-200 text-primary-900 px-4 py-2 rounded-lg hover:text-secondary-600"
              >
                <i class="pi pi-reply text-x"></i>
                <div>Reply</div>
              </button>
              <!-- <button
                @click="getAutoReply"
                class="flex items-center justify-center gap-2 bg-primary-300 text-primary-900 px-4 py-2 rounded-lg hover:text-secondary-600"
              >
                <i class="pi pi-microchip-ai"></i>
                Auto Reply
              </button> -->
              <button
                @click="uploadAttachment"
                class="flex items-center gap-2 bg-primary-200 text-primary-900 px-4 py-2 rounded-lg hover:text-secondary-600"
              >
                <i class="pi pi-paperclip"></i>
                Upload Attachment
              </button>
            </div>
            <!-- <div class="flex gap-4">
              <button @click="importEmail" class="flex items-center gap-1 text-blue-800 rounded-lg hover:text-blue-700">
                <i class="pi pi-file-import"></i>
                Import
              </button>
              <button @click="exportEmail" class="flex items-center gap-1 text-blue-800 rounded-lg hover:text-blue-700">
                <i class="pi pi-file-export"></i>
                Export
              </button>
            </div> -->
          </div>

          <!-- Auto Reply Suggestion -->
          <div v-if="autoReply" class="mt-4 p-4 bg-gray-100 rounded flex-none">
            <h3 class="text-lg font-semibold mb-2">Auto Reply Suggestion</h3>
            <p>{{ autoReply }}</p>
          </div>

          <!-- Hidden File Input -->
          <input type="file" ref="fileInput" @change="handleFileUpload" class="hidden" />
        </div>
        <div
          class="h-full bg-red-100 relative transition-all duration-500"
          :class="{ 'w-1/3': menuOpen, 'w-0': !menuOpen }"
        >
          <div
            @click="menuOpen = !menuOpen"
            class="w-5 h-24 hover:bg-primary-400/50 cursor-pointer transition-all duration-300 flex justify-center items-center text-secondary-900 rounded-l-3xl bg-primary-200 absolute top-1/2 right-full -translate-y-1/2"
          >
            <div
              v-if="Attachments.length > 0 && !menuOpen"
              class="bg-red-600 absolute size-6 pt-[1px] rounded-full flex justify-center items-center top-0 right-2"
            >
              <i class="fa-regular fa-bell fa-beat-fade text-red-50 text-xs"></i>
            </div>
            <ChevronLeftIcon class="size-5 transition-all duration-300" :class="{ 'rotate-180': menuOpen }" />
          </div>
          <div class="size-full bg-primary-100 p-4 overflow-hidden">
            <div class="flex flex-col gap-3">
              <label class="text-lg font-semibold text-secondary-900"> Actions </label>
              <div class="flex gap-2 flex-wrap justify-center">
                <button
                  class="text-xs hover:bg-primary-300 transition-all duration-200 px-2 py-1 text-secondary-800 bg-primary-300/80 rounded-full"
                >
                  Schedule Meeting
                </button>
                <button
                  class="text-xs hover:bg-primary-300 transition-all duration-200 px-2 py-1 text-secondary-800 bg-primary-300/80 rounded-full"
                >
                  Reminders
                </button>
                <button
                  class="text-xs hover:bg-primary-300 transition-all duration-200 px-2 py-1 text-secondary-800 bg-primary-300/80 rounded-full"
                >
                  Follow up
                </button>
                <button
                  class="text-xs hover:bg-primary-300 transition-all duration-200 px-2 py-1 text-secondary-800 bg-primary-300/80 rounded-full"
                >
                  Schedule Meeting
                </button>
                <button
                  class="text-xs hover:bg-primary-300 transition-all duration-200 px-2 py-1 text-secondary-800 bg-primary-300/80 rounded-full"
                >
                  Schedule Meeting
                </button>
              </div>
              <div></div>
            </div>
            <div class="flex flex-col gap-3">
              <label class="text-lg font-semibold text-secondary-900"> Attachments </label>
              <div>
                <div v-for="file of Attachments" class="flex items-center gap-2">
                  <div class="size-10 flex justify-center items-center rounded-md bg-secondary-100/50">
                    <i :class="`fa-regular ${getFileIcon(file)} text-2xl`"></i>
                  </div>
                  <div class="text-secondary-800 truncate" :title="file">{{ file }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <button
      @click="emit('goNext')"
      class="absolute right-1 top-1/2 z-50 bg-secondary-100/60 -translate-y-1/2 p-1 rounded-md hover:bg-secondary-100 transition-colors duration-150"
    >
      <ChevronRightIcon class="size-8" />
    </button>

    <SnoozModal v-model="showSnoozeModal" :email-id="email?.id" @snooze="handleSnooze" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUpdated, computed, watch, VNodeRef } from "vue";
import { useCurrentUserStore } from "../../stores/currentUser";
import { invoke } from "@tauri-apps/api/core";
import { Email, User } from "../../types";
import { GmailMessage } from "../../types";

import {
  ArchiveBoxIcon,
  TrashIcon,
  EnvelopeIcon,
  XMarkIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  PaperClipIcon,
  ClockIcon,
} from "@heroicons/vue/24/outline";
import { getFileIcon } from "../../utils/file";
import { fullNameAbbr } from "../../utils/utils";
import { Draft } from "../../models/task-model";
import emailCommands from "../../commands/emails";
import SnoozModal from "../modals/SnoozModal.vue";
import { snoozeEmail } from "../../commands/snooze";

const cc = ["<EMAIL>", "<EMAIL>"];
const bcc = ["<EMAIL>", "<EMAIL>"];

const dumbThread = [
  "Akram Amokrane",
  "Najib Affane",
  "Akram Amokrane",
  "Najib Affane",
  "Akram Amokrane",
  "Najib Affane",
];

const menuOpen = ref(false);
const showGoToBottom = ref(true);
const showSnoozeModal = ref(false);

const currentUserStore = useCurrentUserStore();

const emit = defineEmits(["goNext", "goPrevious", "close"]);

const props = defineProps({
  emails: Object,
  threadId: String,
  access_token: String,
  nextDisabled: Boolean,
  prevDisabled: Boolean,
});

const drafts = ref<Draft[]>([]);

const threadEmails = ref<Email[]>([]);
const emails = computed<Email[] | null>(() => {
  if (threadEmails.value.length > 0) {
    return threadEmails.value;
  }
  return props.emails
    ? Array.from(new Map((props.emails as Email[]).map((item: Email) => [item.id, item])).values())
    : null;
});
const email = ref<Email | null>(emails.value ? emails.value[0] : null);
const Attachments = computed(() =>
  email.value?.attachments ? (JSON.parse(email.value?.attachments) as string[]).filter((file) => file != "") : []
);

const draft = computed<Draft | undefined>(() => {
  return drafts.value.find((d) => d.parent_email_id === email.value?.id);
});

const autoReply = ref("");
const bodyHtml = ref("");
const replyEmail = () => {
  alert("Reply functionality to be implemented");
};
const localAccessToken = ref(props.access_token);

watch(email, async () => {
  bodyHtml.value = "";
  await fetchThreadEmails();
});

const fetchTry = ref(0);

const decodeBase64Utf8 = (base64: any) => {
  // Gmail encodes with URL-safe base64 (using - and _), so convert first
  base64 = base64.replace(/-/g, "+").replace(/_/g, "/");

  // decode to binary string
  const binaryString = atob(base64);

  // convert binary string to Uint8Array
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // decode UTF-8 bytes to string
  return new TextDecoder("utf-8").decode(bytes);
};
let emailMessages: any = [];

const extractHeader = (headers: any, name: string) => {
  return headers.find((h: any) => h.name.toLowerCase() === name.toLowerCase())?.value || "";
};

interface EmailWithBody extends Email {
  body: string;
}

const fetchThreadEmails = async () => {
  const threadId = props.threadId;
  const accessToken = localAccessToken.value;

  try {
    const response = await fetch(`https://www.googleapis.com/gmail/v1/users/me/threads/${threadId}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      if (response.status === 401) {
        const newAccessToken = await refreshAccessToken();
        if (newAccessToken) {
          localAccessToken.value = newAccessToken;
          return fetchThreadEmails();
        } else {
          console.error("Failed to refresh token");
          return;
        }
      } else {
        console.error("Failed to fetch thread:", response.status);
        return;
      }
    }

    const data = await response.json();
    const messages = data.messages;

    const getMessages = messages.map((msg: GmailMessage) => {
      const headers = msg.payload?.headers;
      const bodyPart = findHtmlPart(msg.payload);

      let decodedHtml = "";

      if (bodyPart?.body?.data) {
        decodedHtml = decodeBase64Utf8(bodyPart.body.data);
      } else if (msg.payload?.body?.data) {
        decodedHtml = decodeBase64Utf8(msg.payload.body.data);
      } else {
        decodedHtml = "(No HTML body)";
      }

      const cleanedHtml = cleanGmailReply(decodedHtml);

      const parsedEmail: EmailWithBody = {
        id: msg.id,
        from: extractHeader(headers, "From"),
        to: extractHeader(headers, "To"),
        cc: extractHeader(headers, "Cc"),
        bcc: extractHeader(headers, "Bcc"),
        date: extractHeader(headers, "Date"),
        subject: extractHeader(headers, "Subject"),
        snippet: msg.snippet,
        body: cleanedHtml,
      };

      return parsedEmail;
    });
    emailMessages = getMessages;
  } catch (error) {
    console.error("Error fetching thread:", error);
  }
};

function findHtmlPart(part: any): any {
  if (!part) return null;
  if (part.mimeType === "text/html" && part.body?.data) return part;
  if (part.parts) {
    for (const sub of part.parts) {
      const found = findHtmlPart(sub);
      if (found) return found;
    }
  }
  return null;
}

function cleanGmailReply(html: string): string {
  // Remove Gmail quote block
  html = html.replace(/<div class="gmail_quote">[\s\S]*?<\/div>/gi, "");

  // Remove blockquotes (common in replies)
  html = html.replace(/<blockquote[^>]*>[\s\S]*?<\/blockquote>/gi, "");

  // Remove lines like "On Jan 1, 2024, John Doe wrote:"
  html = html.replace(/<div[^>]*>On\s.+?wrote:<\/div>[\s\S]*$/i, "");

  html = html.replace(/<p>\s*<\/p>/gi, "");

  html = html.replace(/<span>\s*<\/span>/gi, "");
  // Remove typical Gmail signature separators
  html = html.replace(/--\s*<br>/gi, "");
  // Remove excessive <br> lines (2 or more)
  html = html.replace(/(<br\s*\/?>\s*){2,}/gi, "<br>");
  html = html.trim();
  return html;
}

const showFields = ref<Record<number, Record<string, boolean>>>({});

const toggleField = (index: number, field: string) => {
  if (!showFields.value[index]) showFields.value[index] = {};
  showFields.value[index][field] = !showFields.value[index][field];
};

// Always show only names
const getNameOnly = (input: string): string => {
  return input
    .split(",")
    .map((entry) => {
      const match = entry.match(/^(.*)<(.*)>$/);
      return match ? match[1].trim() : entry.trim();
    })
    .join(", ");
};

// Always show only emails
const getEmailOnly = (input: string): string => {
  return input
    .split(",")
    .map((entry) => {
      const match = entry.match(/^(.*)<(.*)>$/);
      return match ? match[2].trim() : entry.trim();
    })
    .join(", ");
};
const getDisplayName = (input: string, index: number, field: string): string => {
  const isExpanded = showFields.value[index]?.[field];

  return input
    .split(",")
    .map((entry) => {
      const match = entry.match(/^(.*)<(.*)>$/);
      if (!match) return entry.trim();

      const name = match[1].trim();
      const email = match[2].trim();
      return isExpanded ? email : name;
    })
    .join(", ");
};
const formatDate = (raw: string) => {
  const date = new Date(raw);
  return date.toLocaleString("en-US", {
    dateStyle: "medium",
    timeStyle: "short",
  });
};

// Function to refresh the access token
async function refreshAccessToken() {
  // return invoke("js2rs", { message: "get_fresh_accesstoken" }).then((data: any) => {
  //   try {
  //     const me_data = JSON.parse(data);
  //     //  console.log(me_data);

  //     // Assign parsed data to the `User` type
  //     const user: User = {
  //       id: me_data.id,
  //       email: me_data.email,
  //       verified_email: me_data.verified_email,
  //       name: me_data.name,
  //       given_name: me_data.given_name,
  //       family_name: me_data.family_name,
  //       picture: me_data.picture,
  //       refreshToken: me_data.refreshToken,
  //       expires_in: me_data.expire_in,
  //       access_token: me_data.access_token,
  //       username: "",
  //       locale: "",
  //       created_at: "",
  //       updated_at: "",
  //       last_login: "",
  //       login_count: 0,
  //       role: "",
  //       is_active: false,
  //     };

  //     currentUserStore.storeUserInfo(user);

  //     //  console.log("Access token refreshed successfully.");

  //     return me_data.access_token; // Return the new access token
  //   } catch (err) {
  //     console.error("Error parsing data or updating user:", err);
  //     return null;
  //   }
  // });

  return await emailCommands.getFreshAccessToken();
}

// Function to retrieve the updated access token from the store
async function getUpdatedAccessToken() {
  const user = currentUserStore.currentUser;
  return user ? user.access_token : null;
}

async function getThreadDrafts(id: string) {
  const d = await invoke<Draft[]>("thread_waiting_drafts", { threadId: id });
  drafts.value = d ?? [];
}

async function getThreadEmails(id: string) {
  const emails = await invoke<Email[]>("list_thread_emails", { threadId: id });
  //  console.log("Thread emails ==>", emails);
  if (emails.length > 0) {
    await getThreadDrafts(id);
    threadEmails.value = emails;
    email.value = emails[0];
  }
}

function onScroll(e: any) {
  const y = e.target.scrollTop;
  const scrollHeight = e.target.scrollHeight;
  const clientHeight = e.target.clientHeight;
  const atBottom = y + clientHeight >= scrollHeight - 1;
  showGoToBottom.value = !atBottom;
  //  console.log("Element Scroll Top:", y, atBottom);
}

function GoToBottom(e: any) {
  const el = document.getElementById("html-container");
  if (el) {
    //  console.log(el.scrollHeight);
    el.scrollTo({
      top: el.scrollHeight,
      behavior: "smooth",
    });
  }
}

async function sendDraft(draft: Draft) {
  //  console.log("Sending email");
  invoke("reply_email", {
    originalMessageId: draft.parent_email_id,
    recipient: draft.to,
    subject: draft.subject,
    body: draft.body,
    attachments: [],
  })
    .then(async () => {
      await invoke("draft_status", { draftId: draft.id, status: "accepted" });
      //  console.log("Draft Accepted");
    })
    .catch((e) => {
      console.error("Error in sending email", e);
    });
}

async function refuseDraft(draft: Draft) {
  //  console.log("Refusing email");
  await invoke("draft_status", { draftId: draft.id, status: "accepted" });
  //  console.log("Draft Refused");
}

// Fetch the email when the component is mounted
onMounted(async () => {
  await fetchThreadEmails();
  document.body.classList.add("overflow-hidden");
  //  console.log("Email", email.value);
  //  console.log("Emails =>", props.emails);
  const id = email.value?.thread_id ?? props.threadId;
  if (id) await getThreadEmails(id);
});

onUpdated(() => {
  fetchThreadEmails();
  document.body.classList.add("overflow-hidden");
});

const getAutoReply = async () => {
  try {
    const response = await fetch("http://localhost:5001/generate_reply", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ content: email.value?.snippet }),
    });
    const data = await response.json();
    autoReply.value = data.reply;
  } catch (error) {
    console.error("Error generating auto reply:", error);
  }
};

const markEmailsAsSpamInGmail = async () => {
  try {
    await invoke("mark_email_as_spam_in_gmail", { emailId: email.value?.id });
    //  console.log("Emails successfully marked as spam in Gmail.");
  } catch (error) {
    console.error("Failed to mark emails as spam in Gmail:", error);
  }
};

const markAsUnread = async () => {
  try {
    await invoke("mark_email_as_unread_in_gmail", { emailId: email.value?.id });
    //  console.log("Emails successfully marked as unread in Gmail.");
  } catch (error) {
    console.error("Failed to mark emails as unread in Gmail:", error);
  }
};

// Function to delete emails from Gmail
const deleteEmail = async () => {
  try {
    await invoke("delete_email_from_gmail", { emailId: email.value?.id, accessToken: await getUpdatedAccessToken() });
    //  console.log("Emails successfully deleted from Gmail.");
  } catch (error) {
    console.error("Failed to delete emails from Gmail:", error);
  }
};

const archiveEmail = async () => {};

// Function to open snooze modal
const openSnoozeModal = () => {
  showSnoozeModal.value = true;
};

// Function to handle snooze action
const handleSnooze = async (emailId: string | undefined, snoozeUntil: Date) => {
  try {
    if (!emailId) {
      console.error("No email ID provided for snoozing");
      return;
    }

    console.log(`Snoozing email ${emailId} until ${snoozeUntil.toISOString()}`);

    // Call the snooze command
    const result = await snoozeEmail(emailId, snoozeUntil);

    console.log("Email snoozed successfully:", result);

    // Close the modal
    showSnoozeModal.value = false;

    // Optionally emit close event to go back to email list
    emit("close");
  } catch (error) {
    console.error("Failed to snooze email:", error);
    // You might want to show a user-friendly error message here
  }
};

const uploadAttachment = () => {
  const fileInput = ref("fileInput");
  // fileInput.value.click();
};

const handleFileUpload = (event: Event) => {
  // const file = event.target.files[0];
  // if (file) {
  //   alert(`File "${file.name}" selected for upload.`);
  // }
};

const importEmail = () => {
  alert("Import functionality to be implemented");
};

const exportEmail = () => {
  alert("Export functionality to be implemented");
};
</script>

<style scoped>
/* Additional styling to improve the layout */

/* This class will disable scrolling on the body */
</style>
