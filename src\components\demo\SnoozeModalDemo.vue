<template>
  <div class="p-8 bg-primary min-h-screen">
    <div class="max-w-md mx-auto">
      <h1 class="text-2xl font-bold text-dark-600 mb-6">Snooze Modal Demo</h1>
      
      <div class="bg-primary-200 rounded-lg p-4 mb-6">
        <h3 class="font-semibold text-dark-600 mb-2">Test Email</h3>
        <p class="text-sm text-secondary-600 mb-2">From: <EMAIL></p>
        <p class="text-sm text-secondary-600 mb-4">Subject: Important Meeting Tomorrow</p>
        
        <button
          @click="openSnoozeModal"
          class="flex items-center gap-2 px-4 py-2 bg-secondary-500 text-white rounded-lg hover:bg-secondary-600 transition-colors duration-200"
        >
          <ClockIcon class="size-4" />
          Snooze Email
        </button>
      </div>

      <!-- Active Snoozed Emails -->
      <div class="bg-primary-200 rounded-lg p-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="font-semibold text-dark-600">Snoozed Emails</h3>
          <button
            @click="refreshSnoozedEmails"
            class="text-secondary-600 hover:text-secondary-700 transition-colors duration-200"
          >
            <ArrowPathIcon class="size-4" />
          </button>
        </div>
        
        <div v-if="snoozedEmails.length === 0" class="text-sm text-secondary-600">
          No snoozed emails
        </div>
        
        <div v-else class="space-y-2">
          <div
            v-for="email in snoozedEmails"
            :key="email.id"
            class="bg-primary-100 rounded p-3 text-sm"
          >
            <div class="font-medium text-dark-600">Email ID: {{ email.email_id }}</div>
            <div class="text-secondary-600">
              Snooze until: {{ formatDate(email.snoozed_until) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Snooze Modal -->
    <SnoozModal 
      v-model="showSnoozeModal" 
      :email-id="testEmailId"
      @snooze="handleSnooze"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ClockIcon, ArrowPathIcon } from '@heroicons/vue/24/outline'
import SnoozModal from '../modals/SnoozModal.vue'
import { snoozeEmail, getActiveSnoozedEmails, type EmailLog } from '../../commands/snooze'

// Reactive state
const showSnoozeModal = ref(false)
const testEmailId = ref('demo-email-123')
const snoozedEmails = ref<EmailLog[]>([])

// Methods
const openSnoozeModal = () => {
  showSnoozeModal.value = true
}

const handleSnooze = async (emailId: string | undefined, snoozeUntil: Date) => {
  try {
    if (!emailId) return
    
    console.log(`Demo: Snoozing email ${emailId} until ${snoozeUntil.toISOString()}`)
    
    // Call the snooze function
    const result = await snoozeEmail(emailId, snoozeUntil)
    console.log('Snooze result:', result)
    
    // Refresh the snoozed emails list
    await refreshSnoozedEmails()
    
    // Close modal
    showSnoozeModal.value = false
  } catch (error) {
    console.error('Failed to snooze email:', error)
  }
}

const refreshSnoozedEmails = async () => {
  try {
    snoozedEmails.value = await getActiveSnoozedEmails()
  } catch (error) {
    console.error('Failed to fetch snoozed emails:', error)
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString()
}

// Load snoozed emails on mount
onMounted(() => {
  refreshSnoozedEmails()
})
</script>

<style scoped>
/* Add any custom styles here */
</style>
