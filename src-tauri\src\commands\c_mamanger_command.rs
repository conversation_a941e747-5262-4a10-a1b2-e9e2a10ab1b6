use crate::{
    models::email_logs::{EmailLog, NewEmailLog},
    services::email_log_service,
};
use chrono::NaiveDateTime;
use tokio::time::{self, Duration};
use tracing::log::{info, error};
use crate::emails_service;
use uuid::Uuid;
use chrono::Utc;
use std::sync::{Arc, atomic::{AtomicBool, Ordering}};
use tauri::{AppHandle, State};

#[tauri::command]
pub fn add_email_snooze(email_id: String, snoozed_until: String) -> Result<EmailLog, String> {
    let snooze_time = NaiveDateTime::parse_from_str(&snoozed_until, "%Y-%m-%dT%H:%M:%S")
        .map_err(|e| format!("Invalid datetime: {}", e))?;

    let new_log = NewEmailLog {
        id: Uuid::new_v4().to_string(),
        email_id,
        snoozed_until: snooze_time,
        created_at: Utc::now().naive_utc(),
    };

    email_log_service::insert_snooze(new_log)
}

#[tauri::command]
pub fn get_active_snoozed_emails() -> Vec<EmailLog> {
    email_log_service::get_active_snoozed()
}

#[tauri::command]
pub fn resurface_due_emails() -> Result<usize, String> {
    email_log_service::resurface_due_emails()
}

#[tauri::command]
pub fn get_all_email_logs() -> Vec<EmailLog> {
    email_log_service::get_all()
}

#[tauri::command]
pub fn get_email_log_by_id(id: String) -> Option<EmailLog> {
    email_log_service::get_by_id(id)
}

#[tauri::command]
pub fn delete_email_log(id: String) -> Result<(), String> {
    email_log_service::delete(id)
}

#[tauri::command]
pub fn run_cm_startup_sync() -> Result<usize, String> {
    let resurfaced = email_log_service::resurface_due_emails()?;

    // Optionally cache or warm up categories
    let _ = emails_service::get_today_categories(); // ignore failure

    Ok(resurfaced)
}


/// Shared atomic flag to avoid multiple cron spawns
#[derive(Default)]
pub struct CronControl {
    pub cm_cron_started: AtomicBool,
}

#[tauri::command]
pub fn start_cm_cron(app: AppHandle, control: State<Arc<CronControl>>) -> Result<(), String> {
    if control.cm_cron_started.load(Ordering::Relaxed) {
        return Ok(()); // Already running, skip
    }

    control.cm_cron_started.store(true, Ordering::Relaxed);
    start_cm_cron_loop(app);
    Ok(())
}


/// Starts a categorized email manager (CM) lifecycle cron loop
/// - Resurfaces due emails
/// - Optionally updates daily category stats
/// - Runs every 60 seconds
pub fn start_cm_cron_loop(_app: AppHandle) {
    tokio::spawn(async move {
        let mut interval = time::interval(Duration::from_secs(60)); // every 1 minute

        loop {
            interval.tick().await;

            match email_log_service::resurface_due_emails() {
                Ok(count) => {
                    if count > 0 {
                        info!("🔁 Resurfaced {} snoozed emails", count);

                        match emails_service::get_today_categories() {
                            Ok(updated_cats) => {
                                info!("✅ Refreshed {} email categories after resurfacing", updated_cats.len());

                                // Optional: emit update to frontend
                                // let _ = _app.emit_all("refresh_cm", updated_cats);
                            }
                            Err(e) => {
                                error!("⚠️ Failed to update categories after resurfacing: {}", e);
                            }
                        }
                    }
                }
                Err(e) => {
                    error!("❌ Failed to run CM resurface lifecycle: {}", e);
                }
            }
        }
    });
}
