{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[6423159271834319385, "build_script_build", false, 17013795312578762737], [17031207854228184515, "build_script_build", false, 4833849940976976259], [10755362358622467486, "build_script_build", false, 2213624108158900676], [371826037427084582, "build_script_build", false, 9959874339731111545], [3834743577069889284, "build_script_build", false, 6084073592759035714], [1582828171158827377, "build_script_build", false, 3375193970254266695]], "local": [{"RerunIfChanged": {"output": "debug\\build\\Oway-5bf41326db06686a\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}