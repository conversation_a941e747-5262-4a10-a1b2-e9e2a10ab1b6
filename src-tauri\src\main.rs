#![cfg_attr(all(not(debug_assertions), target_os = "windows"), windows_subsystem = "windows")]
#![allow(unused)]

mod commands;
mod db;
mod email_generator;
mod emails_functions;
mod enhancedFunctions;
mod google_api_functions;
mod google_auth;
mod models;
mod notification;
mod open_ai;
mod schema;
mod services;
mod settings;
mod windows;
use crate::models::email::Email;
use crate::models::email_category::EmailCategory;
use commands::drafts_command::{
    draft_status,
    task_draft,
    thread_drafts,
    thread_waiting_drafts,
    waiting_drafts,
};
use enhancedFunctions::email_priority::categorize_email_from_metadata;
use models::phone_context::PhoneCallContext;
use reqwest::header::{ Keys, AUTHORIZATION, CONTENT_TYPE }; // Import the necessary headers
use std::collections::HashMap;
use tokio::sync::mpsc;
use tokio::sync::mpsc::{ Receiver, Sender };

use chrono::NaiveDateTime;
use chrono::{ DateTime, Utc };
use libsqlite3_sys::NOT_WITHIN;
use models::email_category::NewEmailCategory;
use reqwest::Client;
use serde_json::Value;
use services::emails_service;
use tokio::time;
use uuid::Uuid;

use commands::c_mamanger_command::start_cm_cron;
use commands::ask_command::ask;
use commands::js2rs_command::authenticate_user;
use commands::js2rs_command::js2rs;

use crate::google_api_functions::check_and_refresh_token_unread::check_and_refresh_token_unread;
use crate::google_api_functions::refresh_access_token::refresh_access_token;
use commands::js2rs_command::initialize_contexts;
use commands::js2rs_command::set_password;

use commands::delete_email::delete_email_from_gmail;
use commands::delete_email::mark_email_as_read_in_gmail;
use commands::delete_email::mark_email_as_spam_in_gmail;
use commands::delete_email::mark_email_as_unread_in_gmail;
use commands::email_category_commands::*;
use commands::email_commands::*;
use commands::fetch_command::fetch_all_emails;
use commands::fetch_command::*;
use commands::fetch_unread_emails::fetch_unread_emails;
use commands::fetch_unread_emails::*;
use commands::message_commands::*;
use commands::session_commands::*;
use commands::settings_commands::*;
use commands::tasks_command::*;
use commands::auth_command::*;
use std::sync::Arc;
use tokio::sync::RwLock;

use commands::delete_email::send_email;
use tokio::sync::Notify;

use commands::{ assistant_commands::*, email_category_commands };
use dotenv::dotenv;
use home::home_dir;
use oauth2::basic::BasicTokenType;
use oauth2::{
    basic::BasicClient,
    reqwest::async_http_client,
    revocation::StandardRevocableToken,
    AccessToken,
    AuthUrl,
    AuthorizationCode,
    ClientId,
    ClientSecret,
    CsrfToken,
    EmptyExtraTokenFields,
    PkceCodeChallenge,
    RedirectUrl,
    RefreshToken,
    RevocationUrl,
    Scope,
    StandardTokenResponse,
    TokenResponse,
    TokenUrl,
};
use regex::Regex;
use reqwest;
// use rust_bert::pipelines::ner::NERModel;
// use rust_bert::pipelines::sentiment::{SentimentModel, SentimentPolarity};

use models::user_data::UserData;
use models::user_perference::EmailContext;
use serde::{ Deserialize, Serialize };
use serde_json::json;
use services::assistants_service;
use services::email_category_service;
use services::email_channel::{ get_email_sender, set_email_sender };
use std::collections::HashSet;
use std::error::Error;
use std::fs;
use std::fs::read_to_string;
use std::fs::File;
use std::io;
use std::io::{ BufRead, BufReader, Write };
use std::time::{ Duration, Instant };
use tauri::{ command, Emitter };
use tauri::State;
use tauri::{ Manager, RunEvent, WindowEvent };
use tokio::sync::Mutex;
use tokio::sync::MutexGuard;
use tokio::task;
use tracing::{ error, info, warn };

use models::app_data::AppData;
use once_cell::sync::OnceCell;

use std::cmp;

// use rust_bert::pipelines::summarization::SummarizationModel;
use std::env;
use std::process::{ Command, Output };

// use rust_bert::pipelines::common::ModelType;
// use rust_bert::pipelines::text_generation::{TextGenerationConfig, TextGenerationModel};

use google_cloud_pubsub::client::{ Client as PubSubClient, ClientConfig };
use google_cloud_pubsub::subscription::ReceiveConfig;
use tokio_util::sync::CancellationToken;

use futures::future::BoxFuture;
use tokio::time::sleep; // To handle async closures

async fn save_new_token_to_db(
    google_id: &str,
    new_token: &AccessToken
) -> Result<(), Box<dyn Error>> {
    // Implement your logic to save the new token to your database
    Ok(())
}

pub fn init_background_tasks(email_rx: Receiver<Email>) {
    // Spawn DB/email writer task
    task::spawn(async move {
        let mut email_rx = email_rx;
        while let Some(email) = email_rx.recv().await {
            if let Err(e) = emails_service::store_new_email(&email) {
                eprintln!("❌ Failed to store email: {:?}", e);
            }
            tokio::time::sleep(Duration::from_millis(10)).await;
        }
    });

    // Spawn other setup tasks
    task::spawn(async {
        db::init();
        settings::Settings::init();
        assistants_service::init();
    });
}

#[tokio::main]
async fn main() {
    dotenv().ok();
    tracing_subscriber::fmt::init();
    let cancel_token: CancellationToken = CancellationToken::new();

    tauri::async_runtime::set(tokio::runtime::Handle::current());
    let (email_tx, email_rx) = tokio::sync::mpsc::channel::<Email>(1000);

    // ✅ Set the global channel
    set_email_sender(email_tx);

    // ✅ Start background email DB writer
    init_background_tasks(email_rx);
    // task::spawn(async move {
    //     db::init();
    //     settings::Settings::init();
    //     assistants_service::init();
    // });
    let venv_path = "./tauri-python-env/"; // Define the path to your virtual environment
    // let _ = fix_path_env::fix(); // <---- Add this
    // let model = SummarizationModel::new(Default::default())
    //     .expect("Failed to initialize the summarization model");

    // // Activate the virtual environment only once at the start of your application
    // match activate_virtual_env(venv_path) {
    //     Ok(_) => {
    //         println!("Virtual environment activated successfully.");
    //     }
    //     Err(e) => {
    //         eprintln!("Failed to activate virtual environment: {}", e);
    //         return; // Exit the application if the virtual environment activation fails
    //     }
    // }
    // Set up the Tauri application
    let app = tauri::Builder
        ::default()
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .manage(Arc::new(cancel_token)) // Manage the cancellation token separately
        .manage(
            Arc::new(
                RwLock::new(AppData {
                    user_data: Arc::new(RwLock::new(UserData::init_user_data().await)),
                    logged_in: false.into(),
                })
            ) // AppData {
            // user_data: Arc::new(Mutex::new(UserData::init_user_data())), // Wrap UserData in Arc and Mutex
            // logged_in: false.into(),
            //log in state
            //db: establish_connection(&database_name).into(),
            // }
        )
        .setup(|app| {
            let handle_clone = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                println!("Setting up windows...");
                if let Err(e) = windows::setup(handle_clone).await {
                    eprintln!("Setup failed: {}", e);
                }
            });
            Ok(())
        })
        // AppData to manage
        .invoke_handler(
            tauri::generate_handler![
                start_cm_cron,
                get_task_stats,
                list_tasks,
                task_draft,
                thread_drafts,
                thread_waiting_drafts,
                list_subtasks,
                get_task,
                get_subtask,
                new_task,
                new_subtask,
                delete_task,
                delete_subtask,
                enqueue_task,
                enqueue_subtask,
                complete_task,
                complete_subtask,
                reschedule_task,
                reschedule_subtask,
                get_next_task,
                get_next_subtask,
                get_queue_count,
                ask,
                new_session,
                list_sessions,
                get_session,
                get_email_category,
                new_email_category,
                new_email,
                delete_emails,
                delete_email,
                list_email_categories,
                delete_email_category,
                get_today_categories,
                list_emails,
                list_messages,
                delete_session,
                get_settings,
                set_settings,
                create_assistant,
                list_assistants,
                get_assistant,
                update_assistant,
                js2rs,
                authenticate_user,
                initialize_contexts,
                set_password,
                fetch_all_emails,
                fetch_unread_emails,
                delete_email_from_gmail,
                mark_email_as_read_in_gmail,
                mark_email_as_spam_in_gmail,
                mark_email_as_unread_in_gmail,
                send_email,
                list_thread_emails,
                test_process_emails,
                draft_status,
                reply_email,
                waiting_drafts,
                windows::start_main_window,
                windows::start_auth_window,
                windows::start_settings_window,
                get_local_user_info,
                update_cal_info,
                get_fresh_access_token
            ]
        )
        // .setup(|_app| Ok(()))
        .build(tauri::generate_context!())
        .unwrap();

    app.run(move |_app_handler, _event| {
        if let RunEvent::ExitRequested { api, .. } = &_event {
            println!("Exit requested: {:?}", _event);
        }
    });
}

// use once_cell::sync::Lazy;
// pub const MAIN_DATA_FILENAME: &str = r#".tauri_oauth"#;

// #[derive(serde::Serialize, serde::Deserialize, Debug, Clone, Default)]
// /// # User
// /// struct for the google user data
// pub struct User {
//     pub id: String,
//     pub email: String,
//     pub verified_email: bool,
//     pub name: String,
//     pub given_name: String,
//     pub family_name: String,
//     pub picture: String,
//     // pub locale: String,
// }

// pub struct UserCredentials {
//     google_id: String,
//     email: String,
//     name: String,
//     access_token: Option<String>,
//     refresh_token: Option<String>, // Make it optional,
//     token_expiry: Option<String>,  // Make it optional
// }

// impl UserCredentials {
//     pub fn new() -> Self {
//         UserCredentials {
//             google_id: "".to_string(),
//             email: "".to_string(),
//             name: "".to_string(),
//             access_token: Some("".to_string()),
//             refresh_token: Some("".to_string()),
//             token_expiry: Some("".to_string()),
//         }
//     }
// }

// impl User {
//     pub fn new(
//         id: String,
//         email: String,
//         verified_email: bool,
//         name: String,
//         given_name: String,
//         family_name: String,
//         picture: String,
//     ) -> Self {
//         User {
//             id,
//             email,
//             verified_email,
//             name,
//             given_name,
//             family_name,
//             picture,
//         }
//     }
// }

// #[derive(serde::Serialize, serde::Deserialize, Debug, Clone, Default)]
/// # UserData
/// are the central data of the application and are stored in a local file and <br>
/// read with the start of the server or initialized if the file does not yet exist.
/// - user
/// - refresh_token
// pub struct UserData {
//     pub user: User,
//     pub refresh_token: Option<oauth2::RefreshToken>,
//     pub expire_in: Option<Duration>,
//     pub access_token: Option<oauth2::AccessToken>,
//     pub issued_at: Option<DateTime<Utc>>, // Add this field
// }

// impl UserData {
//     ///constructor from user_data as clone()
//     pub fn new(
//         user_data: &User,
//         refresh_token: Option<RefreshToken>,
//         expire_in: Option<Duration>,
//         access_token: Option<AccessToken>,
//     ) -> Self {
//         info!("UserData new()");

//         UserData {
//             user: user_data.clone(),
//             refresh_token: None,
//             expire_in: None,
//             access_token: None,
//             issued_at: None,
//         }
//     }

//     ///consturctor from file
//     pub fn init_user_data() -> Self {
//         info!("UserData init_main_data()");

//         let home_dir = home_dir().unwrap_or("".into());

//         let file_and_path = format!(
//             "{}/{}",
//             home_dir.to_str().unwrap_or("").to_string(),
//             MAIN_DATA_FILENAME
//         );

//         let user_data_string = read_to_string(file_and_path).unwrap_or("".to_string());

//         let user_data = match serde_json::from_str(&user_data_string) {
//             Ok(result) => result,
//             Err(err) => {
//                 warn!(?err, "warn: ");
//                 UserData {
//                     user: User::new(
//                         "".to_string(),
//                         "".to_string(),
//                         true,
//                         "".to_string(),
//                         "".to_string(),
//                         "".to_string(),
//                         "".to_string(),
//                     ),
//                     refresh_token: None,
//                     expire_in: None,
//                     access_token: None,
//                     issued_at: None,
//                 }
//             }
//         };
//         info!("user_data: {:#?}", user_data);
//         return user_data;
//     }

//     ///set and save the main_data
//     pub fn set(&mut self, email: String, name: String) {
//         self.user.email = email;
//         self.user.name = name;
//         self.save_me();
//     }

//     pub fn set_token(&mut self, refresh_token: Option<oauth2::RefreshToken>) {
//         self.refresh_token = refresh_token.clone();
//         self.save_me();
//     }

//     pub fn set_access_token(&mut self, access_token: Option<oauth2::AccessToken>) {
//         self.access_token = access_token.clone();
//         self.save_me();
//     }

//     pub fn set_exprie_in(&mut self, expire_in: Option<Duration>) {
//         self.expire_in = expire_in.clone();
//         self.save_me();
//     }

//     pub fn set_issued_at(&mut self) {
//         self.issued_at = Some(Utc::now());
//         self.save_me();
//     }

//     ///save refresh token from UserData in file
//     pub fn save_me(&self) {
//         info!("UserData save_me()");

//         let home_dir = home_dir().unwrap_or("".into());

//         let file_and_path = format!(
//             "{}/{}",
//             home_dir.to_str().unwrap_or("").to_string(),
//             MAIN_DATA_FILENAME
//         );

//         let main_data_json = json!(self).to_string();

//         match fs::write(file_and_path, main_data_json) {
//             Ok(_) => {}
//             Err(err) => {
//                 error!(?err, "Error: ");
//             }
//         };
//     }

//     /// ## log_in()
//     /// call ``get_token()`` OAuth and then get the userdate <br>
//     /// ``bool`` Return value returns ``logged_in`` if the OAuth
//     /// was successful and supplied an access token.
//     /// In addition, this token is then used to retrieve the
//     /// user data is retrieved.
//     /// Only if both actions were successful ``true`` is returned.
//     /// is returned.
//     pub async fn log_in(&mut self, window: &tauri::Window) -> bool {
//         let l_do: i32 = 'block: {
//             let (l_access_token, l_refresh_token, l_expires_in) =
//                 match get_token(&window, self.user.email.clone(), self.refresh_token.clone()).await
//                 {
//                     Ok(token) => token,
//                     Err(e) => {
//                         error!("error - Access token could not be retrieved {}", e);

//                         self.user.name = "".to_string();
//                         self.user.email = "".to_string();
//                         self.refresh_token = None;

//                         self.save_me();

//                         return false;
//                     }
//                 };

//             let url = format!(
//                 "https://www.googleapis.com/oauth2/v1/userinfo?alt=json&access_token={:?}",
//                 l_access_token.clone().unwrap().secret()
//             );

//             info!(?l_expires_in, "this my expire time");

//             let resp = match reqwest::get(url).await {
//                 Ok(res) => match res.text().await {
//                     Ok(res_text) => res_text,
//                     Err(e) => {
//                         error!("error - userinfo could not be retrieved {}", e);
//                         return false;
//                     }
//                 },
//                 Err(e) => {
//                     error!("error - userinfo could not be retrieved {}", e);
//                     return false;
//                 }
//             };

//             let userinfo: User = match serde_json::from_str(&resp) {
//                 Ok(result) => {
//                     // info!(?result, "this my result after deserilzation");
//                     result
//                 }
//                 Err(e) => {
//                     info!(?e, "this my result after deserilzation");
//                     User {
//                         id: "".to_string(),
//                         email: "".to_string(),
//                         verified_email: false,
//                         name: "".to_string(),
//                         given_name: "".to_string(),
//                         family_name: "".to_string(),
//                         picture: "".to_string(),
//                         // locale: "".to_string(),
//                     }
//                 }
//             };

//             let url = "http://localhost:9000/api/store_user_credentials/";
//             let token_expiry_str = l_expires_in.map(|duration| {
//                 (Utc::now() + chrono::Duration::from_std(duration).unwrap()).to_rfc3339()
//             });
//             let refresh_token_str = l_refresh_token
//                 .clone()
//                 .map(|token| token.secret().to_string());
//             let access_token_str = l_access_token
//                 .clone()
//                 .map(|token| token.secret().to_string());

//             let user_credentials = UserCredentials {
//                 google_id: userinfo.id.clone().to_string(),
//                 email: userinfo.clone().email.to_string(),
//                 name: userinfo.clone().name.to_string(),
//                 access_token: access_token_str.clone(),
//                 refresh_token: refresh_token_str.clone(),
//                 token_expiry: token_expiry_str,
//             };

//             let client = reqwest::Client::new();
//             // Convert the struct to a JSON string
//             let user_credentials_json = json!({
//                 "google_id": user_credentials.google_id.to_string(),
//                 "email": user_credentials.email,
//                 "name": user_credentials.name,
//                 "access_token": Some(user_credentials.access_token),
//                 "refresh_token": Some(user_credentials.refresh_token),
//                 "token_expiry": Some(user_credentials.token_expiry)
//             });

//             println!("{}", user_credentials_json);

//             match client.post(url).json(&user_credentials_json).send().await {
//                 Ok(response) => {
//                     if response.status().is_success() {
//                         Ok(())
//                     } else {
//                         Err(format!(
//                             "Failed to store user credentials, status: {}",
//                             response.status()
//                         ))
//                     }
//                 }
//                 Err(err) => Err(format!("Failed to send request: {}", err)),
//             };

//             self.user = userinfo;

//             info!(?self.user, "user information after assigning ....");

//             if self.user.email.is_empty() {
//                 //if invalide email clear token
//                 self.set_token(None);
//                 self.set_access_token(None);
//                 self.set_exprie_in(None);
//                 self.set_issued_at();
//                 self.save_me();
//                 return false;
//             }

//             self.save_me();

//             if l_refresh_token.is_some() {
//                 info!("refresh_token found");
//                 self.set_token(l_refresh_token);

//                 // return true;
//             }
//             if l_access_token.is_some() {
//                 info!("l_access_token found");

//                 self.set_access_token(l_access_token);

//                 // return true;
//             }
//             if l_expires_in.is_some() {
//                 info!("l_access_token found");

//                 self.set_exprie_in(l_expires_in);
//                 self.set_issued_at();
//                 return true;
//             }
//             return false;
//             99
//         };

//         false
//     }
// }

/// # AppData
/// is managed via the tauri app
// pub struct AppData {
//     pub user_data: Arc<Mutex<UserData>>,
//     pub logged_in: Mutex<bool>,
// }

// /// # get_token
// /// Function to determine the access token for access to gmail
// ///
// /// https://developers.google.com/identity/protocols/
// async fn get_token(
//     window: &tauri::Window,
//     email: String,
//     refresh_token: Option<oauth2::RefreshToken>,
// ) -> Result<
//     (
//         Option<AccessToken>,
//         Option<oauth2::RefreshToken>,
//         Option<Duration>,
//     ),
//     Box<dyn std::error::Error>,
// > {
//     //get the google client ID and the client secret from .env file
//     dotenv().ok();

//     let google_client_id = ClientId::new(std::env::var("GOOGLE_CLIENT_ID")?);
//     let google_client_secret = ClientSecret::new(std::env::var("GOOGLE_CLIENT_SECRET")?);
//     let auth_url = AuthUrl::new("https://accounts.google.com/o/oauth2/v2/auth".to_string())?; //.expect("Invalid authorization endpoint URL");
//     let token_url = TokenUrl::new("https://www.googleapis.com/oauth2/v3/token".to_string())?; //.expect("Invalid token endpoint URL");

//     // Set up the config for the Google OAuth2 process.
//     let client = BasicClient::new(
//         google_client_id,
//         Some(google_client_secret),
//         auth_url,
//         Some(token_url),
//     )
//     // This example will be running its own server at http://127.0.0.1:1421
//     // See below for the server implementation.
//     .set_redirect_uri(
//         RedirectUrl::new("http://127.0.0.1:1421".to_string())?, //.expect("Invalid redirect URL"),
//     )
//     // Google supports OAuth 2.0 Token Revocation (RFC-7009)
//     .set_revocation_uri(
//         RevocationUrl::new("https://oauth2.googleapis.com/revoke".to_string())?, //.expect("Invalid revocation endpoint URL"),
//     ); //.set_introspection_uri(introspection_url);

//     if refresh_token.is_some() {
//         println!("get_token() refresh_token found");

//         match client
//             .exchange_refresh_token(&refresh_token.unwrap().clone())
//             .request_async(async_http_client)
//             .await
//         {
//             Ok(token_response) => {
//                 let access_token = token_response.access_token().clone();
//                 let refresh_token = token_response.refresh_token().cloned();
//                 let experies_in: Option<Duration> = token_response.expires_in().clone();

//                 return Ok((Some(access_token), refresh_token, experies_in));
//             }
//             Err(_) => {}
//         };
//         println!("get_token() refresh_token not valid, login required");
//     }

//     // Google supports Proof Key for Code Exchange (PKCE - https://oauth.net/2/pkce/).
//     // Create a PKCE code verifier and SHA-256 encode it as a code challenge.
//     let (pkce_code_challenge, pkce_code_verifier) = PkceCodeChallenge::new_random_sha256();

//     // Generate the authorization URL to which we'll redirect the user.
//     let (authorize_url, csrf_state) = client
//         .authorize_url(CsrfToken::new_random)
//         // This example is requesting access to the "gmail" features and the user's profile.
//         //.add_scope(Scope::new("https://mail.google.com".into()))
//         .add_scope(Scope::new("profile email".into()))
//         .add_scope(Scope::new(
//             "https://www.googleapis.com/auth/gmail.readonly".into(),
//         ))
//         .add_scope(Scope::new(
//             "https://www.googleapis.com/auth/gmail.modify".into(),
//         ))
//         .add_scope(Scope::new(
//             "https://www.googleapis.com/auth/gmail.send".into(),
//         ))
//         .add_scope(Scope::new(
//             "https://www.googleapis.com/auth/gmail.labels".into(),
//         ))
//         // This example is also requesting access to the user's profile and email.
//         .add_scope(Scope::new("email".into()))
//         .add_extra_param("access_type", "offline")
//         .add_extra_param("login_hint", email)
//         //.add_extra_param("prompt", "none")
//         .set_pkce_challenge(pkce_code_challenge)
//         .url();

//     println!("The authorization URL is:\n{}\n", authorize_url.to_string());

//     let handle = window.app_handle();

//     let login_window = tauri::WindowBuilder::new(
//         &handle,
//         "Google_Login", /* the unique window label */
//         tauri::WindowUrl::External(
//             authorize_url.to_string().parse()?, //.expect("error WindowBuilder WindowUrl parse"),
//         ),
//     )
//     .build()?; //.expect("error WindowBuilder build");
//     login_window.set_title("Google Login");
//     login_window.set_always_on_top(true);

//     // A very naive implementation of the redirect server.
//     let listener = std::net::TcpListener::bind("127.0.0.1:1421")?; //.expect("error TcpListener bind");
//     let local_addr = listener.local_addr()?;

//     let timer = timer::Timer::new();

//     let _guard = timer.schedule_with_delay(chrono::Duration::seconds(65), move || {
//         //the time out as connect to close server
//         let _ = std::net::TcpStream::connect(local_addr);
//     });

//     login_window.on_window_event(move |event| {
//         if let WindowEvent::CloseRequested { api, .. } = &event {
//             info!("event close-requested");
//             let _ = std::net::TcpStream::connect(local_addr); //connect to server to close it
//         };
//     });

//     //this is blocking listener! we use guard schedule for time out
//     for stream in listener.incoming() {
//         let _ = login_window.is_visible()?; //check if login_window is visible

//         if let Ok(mut stream) = stream {
//             info!("listener stream");

//             let code;
//             let state;
//             let errorinfo;
//             {
//                 let mut reader = BufReader::new(&stream);

//                 let mut request_line = String::new();
//                 reader.read_line(&mut request_line)?;

//                 let redirect_url = match request_line.split_whitespace().nth(1) {
//                     Some(url_data) => url_data,
//                     _ => {
//                         login_window.close()?;
//                         break;
//                     }
//                 };
//                 println!("redirect_url: \n{}", redirect_url.clone());
//                 let url = url::Url::parse(&("http://localhost".to_string() + redirect_url))?;

//                 use std::borrow::Cow;
//                 //extract code from url
//                 let code_pair = url
//                     .query_pairs()
//                     .find(|pair| {
//                         let &(ref key, _) = pair;
//                         key == "code"
//                     })
//                     .unwrap_or((Cow::from(""), Cow::from("")));

//                 let (_, value) = code_pair;
//                 code = AuthorizationCode::new(value.into_owned());

//                 //extract state from url
//                 let state_pair = url
//                     .query_pairs()
//                     .find(|pair| {
//                         let &(ref key, _) = pair;
//                         key == "state"
//                     })
//                     .unwrap_or((Cow::from(""), Cow::from("")));

//                 let (_, value) = state_pair;
//                 state = CsrfToken::new(value.into_owned());

//                 //extract error from url
//                 let errorinfo_pair = url
//                     .query_pairs()
//                     .find(|pair| {
//                         let &(ref key, _) = pair;
//                         key == "error"
//                     })
//                     .unwrap_or((Cow::from(""), Cow::from("")));

//                 let (_, value) = errorinfo_pair;
//                 errorinfo = String::from(value.into_owned());
//             }

//             //if error found
//             if !errorinfo.is_empty() {
//                 login_window.close()?;
//                 Err(errorinfo)?
//             }

//             let message = "Verification completed, please close window.";
//             let response = format!(
//                 "HTTP/1.1 200 OK\r\ncontent-length: {}\r\n\r\n{}",
//                 message.len(),
//                 message
//             );
//             stream.write_all(response.as_bytes())?;

//             println!("Google returned the following code:\n{}\n", code.secret());
//             println!(
//                 "Google returned the following state:\n{} (expected `{}`)\n",
//                 state.secret(),
//                 csrf_state.secret()
//             );

//             // Exchange the code with a token.
//             let token_response = match client
//                 .exchange_code(code)
//                 .set_pkce_verifier(pkce_code_verifier)
//                 .request_async(async_http_client)
//                 .await
//             {
//                 Ok(res) => res,
//                 Err(err) => {
//                     login_window.close()?;
//                     Err("--  no permission --")?
//                 }
//             };

//             println!("\n{:#?}", token_response);

//             // println!(
//             //     "\naccess-token:\n{:#?}\ntoken_type:\n{:#?}\
//             //     \nexpires_in\n{:#?}\nrefresh_token\n{:#?}\
//             //     \nscopes\n{:#?}\nextra_fields\n{:#?}",
//             //     token_response.access_token().clone(),
//             //     token_response.token_type().clone(),
//             //     token_response.expires_in().clone(),
//             //     token_response.refresh_token().clone(),
//             //     token_response.scopes().clone(),
//             //     token_response.extra_fields().clone()
//             // );

//             let access_token = token_response.access_token().clone();
//             let refresh_token = token_response.refresh_token().cloned();
//             let experies_in: Option<Duration> = token_response.expires_in().clone();

//             println!("Google returned the following token:\n{:?}\n", access_token);

//             // // Revoke the obtained token
//             // let token_response = token_response.unwrap();
//             // let token_to_revoke: StandardRevocableToken = match token_response.refresh_token() {
//             //     Some(token) => token.into(),
//             //     None => token_response.access_token().into(),
//             // };

//             // client
//             //     .revoke_token(token_to_revoke)
//             //     .unwrap()
//             //     .request_async(async_http_client).await
//             //     //.request(http_client)
//             //     .expect("Failed to revoke token");

//             login_window.close()?; //.expect("error closw login window");

//             return Ok((Some(access_token), refresh_token, experies_in));
//             // The server will terminate itself after revoking the token.
//             break;
//         } else {
//             println!("error on stream");
//             break;
//         }
//     } //listener.incoming() loop

//     Err("-- login window time out --")?

//     //return "".to_string(); //token_result.access_token().clone();
// }

// A function that sends a message from Rust to JavaScript via a Tauri Event
pub fn rs2js<R: tauri::Runtime>(message: String, handle: tauri::AppHandle) {
    let mut sub_message = message.clone();
    sub_message.truncate(50);
    info!(?sub_message, "rs2js");
    match handle.emit("rs2js", message) {
        Ok(_) => {}
        Err(err) => {
            error!(?err);
        }
    };
}

// Function to load blacklisted domains from a file
fn load_blacklisted_domains_from_text() -> Result<HashSet<String>, io::Error> {
    let file = File::open("/enhancedFunctions/blacklist.txt")?; // Corrected directory name
    let reader = BufReader::new(file);

    let blacklisted_domains_: HashSet<String> = reader
        .lines()
        .map(|line| line.expect("Could not read line").trim().to_string())
        .collect();

    Ok(blacklisted_domains_)
}

// #[tauri::command(async)]
// async fn fetch_all_emails(
//     message: String,
//     tone: String,
//     key_points: String,
//     app_data: tauri::State<'_, Arc<RwLock<AppData>>>, // Match this with fetch_unread_emails
// ) -> Result<String, String> {
//     info!(message, "message_handler: ");
//     let timer: u64 = 10;
//     if message == "generate_email" {
//         // Generate text using the helper function

//         let input_context = format!(
//             "Write a professional email to highlight the opportunity of working with our company in logistics. \
//             The key points are: {}. \
//             Begin with a formal introduction, explain the key points in detail, and conclude with a call to action for the next steps.",
//             key_points
//         );
//         let generated_text = generate_email_reply(
//             //model.clone(),
//             &key_points,
//             &tone,
//             // &input_context,
//         )
//         .await;
//         let generated_string = generated_text
//             .as_ref()
//             .map(String::as_str)
//             .unwrap_or("default");
//         // Create JSON with the generated text
//         let user_data_json_data = json!({
//             "email": generated_string,
//         });

//         return Ok(user_data_json_data.to_string());
//     }
//     if message == "fetch_emails" {
//         // let mut logged_in = app_data.logged_in.lock().await;
//         let app_data_arc = app_data.lock().await; // Lock before accessing user_data

//         // let app_data_guard = app_data.lock().unwrap();  // Lock the Mutex

//         let mut user_data = app_data_arc.user_data.lock().await;

//         let mut logged_in = app_data_arc.logged_in.lock().await;

//         // //get the data from the mutex
//         // let mut user_data = app_data.user_data.lock().await.clone();
//         // let mut logged_in_guard = app_data.logged_in.lock().await;
//         // let mut logged_in = logged_in_guard.clone();

//         // *logged_in_guard = true;
//         // drop(logged_in_guard); // Explicitly drop the guard to release the lock if needed

//         // println!("this inside fetch_emails_periodically\n {:#?}", logged_in);
//         let user_name: String = user_data.user.name.clone();
//         // println!("this inside fetch_emails_periodically\n {:#?}", user_name);

//         let user_data_json_data = json!({
//             "id": user_data.user.id,
//             "email": user_data.user.email,
//             "verified_email": user_data.user.verified_email,
//             "name": user_name,
//             "given_name": user_data.user.given_name,
//             "family_name": user_data.user.family_name,
//             "picture": user_data.user.picture,
//             "refresh_token": user_data.refresh_token.as_ref().map(|t| t.secret().clone()),
//             "expire_in": user_data.expire_in.map(|d| d.as_secs()),
//             "issued_at": user_data.issued_at.map(|dt| dt.to_rfc3339()),
//             "access_token": user_data.access_token.as_ref().map(|t| t.secret().clone())
//         });
//         println!(
//             "this inside fetch_emails_periodically \n {:#?}",
//             user_data_json_data
//         );

//         // Check if the access token is expired and refresh if needed
//         if let (Some(issued_at), Some(expire_in)) = (user_data.issued_at, user_data.expire_in) {
//             let expiry_time = issued_at + expire_in;
//             println!(
//                 "this inside check if access token is expried \n {:#?}",
//                 expiry_time
//             );
//             // if chrono::Utc::now() >= expiry_time {
//             println!(
//                 "My access tokent did exprie lets create a new access token \n {:#?}",
//                 user_data.refresh_token.as_ref().unwrap().secret()
//             );

//             let new_token_result =
//                 refresh_access_token(&user_data.refresh_token.as_ref().unwrap().secret()).await;

//             if let Ok(new_token) = new_token_result {
//                 println!("this after refresh_access_token \n {:#?}", new_token);

//                 // let mut user_data = app_data.user_data.lock().await;
//                 let mut user_logged_data = user_data.clone();
//                 // Update the access token
//                 user_data.access_token = Some(new_token);

//                 // Update the issued_at time after refreshing the token
//                 user_data.set_issued_at();
//                 user_data.save_me();

//                 // Drop the logged_in guard if it's held

//                 drop(user_logged_data);

//                 // let user_name = user_data.user.name.clone();
//             }
//             // }
//         }
//         fetch_emails(
//             &user_data.access_token.as_ref().unwrap().secret(),
//             "https://www.googleapis.com/gmail/v1/users/me/messages".to_string(),
//         )
//         .await;
//         return Ok(user_data_json_data.to_string());

//         // return Ok(user_data_json_data.to_string());
//     }

//     //return else
//     Ok("".to_string())
// }

// // Function to load the last saved historyId
// fn load_history_id() -> Option<String> {
//     match std::fs::read_to_string("history_id.txt") {
//         Ok(history_id) => Some(history_id),
//         Err(_) => None, // Return None if the file doesn't exist or an error occurs
//     }
// }

// /// Helper function to generate text based on an input context
// async fn generate_email_reply(
//     key_points: &str,
//     tone: &str
// ) -> Result<String, Box<dyn Error>> {

//     // List of common call-to-action and sign-off phrases
//     let stopping_phrases = vec![
//         "Best regards",
//         "Sincerely",
//         "Kind regards",
//         "Thank you",
//         "Looking forward to hearing from you",
//         "Please let me know if you have any questions",
//         "I look forward to your response"
//     ];

//     let input_context = format!(
//         "Compose a professional email regarding the following scenario:
//         You want to respectfully inform your boss that it is time for you to part ways with the company.
//         The tone should be {}.
//         Begin with a formal greeting, acknowledge your appreciation for the opportunities provided,
//         and conclude with a professional sign-off, expressing your willingness to assist with the transition.",
//         tone
//     );

//     // Define the model configuration
//     let config = TextGenerationConfig {
//         model_type: ModelType::GPT2,
//         max_length: Some(250),   // Set reasonable max length for emails
//         do_sample: true,         // Sampling for more natural output
//         temperature: 0.7,        // Balanced randomness
//         top_p: 0.9,              // Nucleus sampling for coherence
//         repetition_penalty: 1.1, // Avoid repetition
//         early_stopping: false,   // Do not stop early, rely on max_length and natural stop
//         ..Default::default()
//     };

//     // Initialize the TextGenerationModel in a separate blocking task
//     let model = task::spawn_blocking(|| TextGenerationModel::new(config))
//         .await
//         .map_err(|e| format!("Failed to join async task: {}", e))? // Convert JoinError to String
//         .map(|model| Arc::new(Mutex::new(model)))
//         .map_err(|e| {
//             eprintln!("Failed to initialize the text generation model: {:?}", e);
//             Box::new(e) as Box<dyn Error>
//         })?;

//     let model = model.lock().await;

//     // Generate the email content
//     let output = model.generate(&[input_context], None);

//     // Post-process the output: Remove extra text after a stopping phrase
//     let mut processed_output = output.get(0)
//         .map(|text| text.trim().to_string())
//         .unwrap_or_else(|| "No output generated".to_string());

//     // Iterate through the stopping phrases and truncate the email at the first match
//     for phrase in &stopping_phrases {
//         if let Some(index) = processed_output.find(phrase) {
//             // Truncate the text at the position of the stopping phrase
//             processed_output = processed_output[..index + phrase.len()].to_string();
//             break; // Exit the loop after finding the first match
//         }
//     }

//     // Return the processed email content
//     if !processed_output.is_empty() {
//         return Ok(processed_output);
//     } else {
//         return Err(Box::new(std::io::Error::new(
//             std::io::ErrorKind::Other,
//             "No output generated",
//         )));
//     }
// }

// async fn check_and_refresh_token(app_data: tauri::State<'_, AppData>) -> Result<(), String> {
//     let mut user_data = app_data.user_data.lock().await;
//     println!("Access token expired. Refreshing... function");

//     if let (Some(issued_at), Some(expire_in)) = (user_data.issued_at, user_data.expire_in) {
//         let expiry_time = issued_at + expire_in;

//         // Check if the access token is expired
//         if chrono::Utc::now() >= expiry_time {
//             println!("Access token expired. Refreshing...");

//             // Call the refresh_access_token function
//             let new_token_result =
//                 refresh_access_token(&user_data.refresh_token.as_ref().unwrap().secret()).await;

//             if let Ok(new_token) = new_token_result {
//                 println!("New access token obtained: {:#?}", new_token);

//                 // Update the access token
//                 user_data.access_token = Some(new_token);

//                 // Update the issued_at time after refreshing the token
//                 user_data.set_issued_at();

//                 // Save the updated user_data
//                 user_data.save_me();
//             } else {
//                 println!(
//                     "Failed to refresh the access token: {:#?}",
//                     new_token_result.err()
//                 );
//                 return Ok(());
//             }
//         } else {
//             println!("Access token is still valid.");
//         }
//     } else {
//         println!("No issued_at or expire_in found in user_data.");
//     }

//     Ok(())
// }

// async fn check_and_refresh_token_unread(app_data: Arc<Mutex<UserData>>) -> Result<(), String> {
//     let mut user_data = app_data.lock().await; // Lock the mutex to get mutable access to UserData

//     println!("Checking if access token needs refreshing...");

//     if let (Some(issued_at), Some(expire_in)) = (user_data.issued_at, user_data.expire_in) {
//         let expiry_time = issued_at + expire_in;

//         // Check if the access token is expired
//         if chrono::Utc::now() >= expiry_time {
//             println!("Access token expired. Refreshing...");

//             // Call the refresh_access_token function
//             let new_token_result =
//                 refresh_access_token(&user_data.refresh_token.as_ref().unwrap().secret()).await;

//             if let Ok(new_token) = new_token_result {
//                 println!("New access token obtained: {:#?}", new_token);

//                 // Update the access token
//                 user_data.access_token = Some(new_token);

//                 // Assuming you need to update issued_at manually
//                 user_data.issued_at = Some(chrono::Utc::now());

//                 // If save_me is needed, ensure it's implemented
//                 // user_data.save_me(); // Implement this method if necessary
//             } else {
//                 println!(
//                     "Failed to refresh the access token: {:#?}",
//                     new_token_result.as_ref().err() // Use `.as_ref()` to borrow the error without moving
//                 );
//                 return Err(format!(
//                     "Failed to refresh token: {}",
//                     new_token_result.err().unwrap() // This `.unwrap()` is safe because we're inside the `else` block
//                 ));
//             }
//         } else {
//             println!("Access token is still valid.");
//         }
//     } else {
//         println!("No issued_at or expire_in found in user_data.");
//     }

//     Ok(())
// }

// #[tauri::command(async)]
// async fn fetch_unread_emails(app_data: tauri::State<'_, Arc<RwLock<AppData>>>) -> Result<String, String> {
//     println!("Fetching unread emails...");

//     // Lock app_data to access UserData
//     let user_data_arc = app_data.lock().await.user_data.clone(); // Lock before accessing user_data

//     // Step 2: Call `check_and_refresh_token_unread` with `user_data_arc`
//     check_and_refresh_token_unread(user_data_arc.clone())
//         .await
//         .map_err(|e| format!("Failed to refresh token: {}", e))?;

//     // Step 2: Access the updated user_data and get the access token
//     let access_token;
//     {
//         let user_data_lock = user_data_arc.lock().await;
//         // let user_data_lock = user_data.lock().await; // Lock the `Mutex<UserData>`

//         access_token = match &user_data_lock.access_token {
//             Some(token) => token.secret().clone(), // Clone the secret once
//             None => return Err("Access token is missing".to_string()),
//         };
//     } // The locks on `app_data` and `user_data` are released here.

//     // // // Step 3: Fetch unread emails with the access token
//     fetch_emails(
//         &access_token,
//         "https://www.googleapis.com/gmail/v1/users/me/messages?q=is:unread".to_string(),
//     )
//     .await
//     .map_err(|e| format!("Failed to fetch emails: {}", e))?;

//     // Step 4: Clone app_data for spawning notification pool task
//     let app_data_arc = app_data.inner().clone(); // Clone the Arc so it can be moved into a background task

//     // Step 5: Spawn a background task to listen for Gmail updates
//     tokio::spawn(async move {
//         // Initialize PubSub Client with authentication
//         let config = ClientConfig::default().with_auth().await.unwrap();
//         let pubsub_client = PubSubClient::new(config).await.unwrap();

//         // Specify the subscription
//         let subscription =
//             pubsub_client.subscription("projects/secura-424307/subscriptions/OwayDev-sub");

//         // Create a cancellation token
//         let cancel_token = CancellationToken::new();

//         // Optional configuration for receiving messages
//         let receive_config = Some(ReceiveConfig {
//             ..Default::default() // Use default settings for other configurations
//         });

//         loop {
//             let receive_config_clone = receive_config.clone();

//             let user_data_arc = app_data_arc.lock().await.user_data.clone(); // Lock before accessing user_data

//             // Call `check_and_refresh_token_unread`
//             check_and_refresh_token_unread(user_data_arc.clone())
//                 .await
//                 .map_err(|e| format!("Failed to refresh token: {}", e));

//             let user_data_lock = user_data_arc.lock().await;
//             let access_token = user_data_lock.access_token.clone();

//             // Step 3: Call the receive method with the callback function and cancellation token
//             subscription
//                 .receive(
//                     move |message, cancel| {  // Use `move` to capture ownership
//                         let mut history_id =
//                             load_history_id().unwrap_or_else(|| "initial-history-id".to_string());

//                         let access_token_cloned = access_token.clone(); // Move access_token into the closure

//                         async move {
//                             println!("Processing Pub/Sub message...");

//                             let url = format!(
//                                 "https://www.googleapis.com/gmail/v1/users/me/history?startHistoryId={}",
//                                 history_id
//                             );

//                             let access_token_copy: String = access_token_cloned
//                                 .as_ref()
//                                 .unwrap()
//                                 .secret()
//                                 .clone();

//                             fetch_emails(&access_token_copy, url).await;
//                         }
//                     },
//                     cancel_token.clone(),
//                     receive_config_clone,
//                 )
//                 .await
//                 .unwrap();

//             sleep(Duration::from_secs(2)).await;
//         }
//     });

//     // Step 6: Optionally start polling with backoff if necessary (commented out in your original code)
//     // poll_with_backoff(&user_data).await; // Uncomment if needed

//     Ok("Fetched unread emails and started notification listener.".to_string())
// }

// async fn refresh_access_token(refresh_token: &str) -> Result<oauth2::AccessToken, Box<dyn Error>> {
//     println!(
//         " this inside my access token refeshier \n {:#?}",
//         refresh_token
//     );

//     //get the google client ID and the client secret from .env file
//     dotenv().ok();

//     let client_id_string = std::env::var("GOOGLE_CLIENT_ID")
//         .unwrap_or_else(|_| "Missing GOOGLE_CLIENT_ID".to_string());
//     // println!(
//     //     " this inside my access token refeshier  GOOGLE_CLIENT_ID \n {:#?}",
//     //     client_id_string
//     // );
//     let client_secret_string = std::env::var("GOOGLE_CLIENT_SECRET")?;

//     // Convert the String variables to &str
//     let client_id: &str = &client_id_string;
//     let client_secret: &str = &client_secret_string;
//     let token_url = "https://oauth2.googleapis.com/token";

//     let params = [
//         ("client_id", client_id),
//         ("client_secret", client_secret),
//         ("refresh_token", refresh_token),
//         ("grant_type", "refresh_token"),
//     ];

//     let client = Client::new();
//     let response = client.post(token_url).form(&params).send().await?;
//     // println!(
//     //     " this my response for my n access token \n {:#?}",
//     //     response
//     // );
//     if response.status().is_success() {
//         let token_response: oauth2::StandardTokenResponse<EmptyExtraTokenFields, BasicTokenType> =
//             response.json().await?;
//         Ok(oauth2::AccessToken::new(
//             token_response.access_token().secret().clone(),
//         ))
//     } else {
//         Err(format!("Failed to refresh access token: {}", response.status()).into())
//     }
// }

// // async fn fetch_emails(access_token: &str, url: String) -> Result<String, Box<dyn Error>> {
// //     println!(
// //         "Let's start fetching emails with access token: {:#?}",
// //         access_token
// //     );

// //     // Initialize the summarization model using spawn_blocking
// //     let summarization_model = task::spawn_blocking(|| SummarizationModel::new(Default::default()))
// //         .await?
// //         .map(|model| Arc::new(Mutex::new(model)))
// //         .map_err(|e| {
// //             eprintln!("Failed to initialize the summarization model: {:?}", e);
// //             Box::new(e) as Box<dyn Error>
// //         })?;

// //     println!("Summarization model initialized successfully.");

// //     // Initialize the sentiment analysis model using spawn_blocking
// //     let sentiment_model = task::spawn_blocking(|| SentimentModel::new(Default::default()))
// //         .await?
// //         .map(|model| Arc::new(Mutex::new(model)))
// //         .map_err(|e| {
// //             eprintln!("Failed to initialize the sentiment model: {:?}", e);
// //             Box::new(e) as Box<dyn Error>
// //         })?;

// //     println!("Sentiment model initialized successfully.");

// //     // Initialize the NER model using spawn_blocking
// //     let ner_model = task::spawn_blocking(|| NERModel::new(Default::default()))
// //         .await?
// //         .map(|model| Arc::new(Mutex::new(model)))
// //         .map_err(|e| {
// //             eprintln!("Failed to initialize the NER model: {:?}", e);
// //             Box::new(e) as Box<dyn Error>
// //         })?;

// //     let mut gmail_api_url = url;
// //     let client = Client::builder()
// //         .timeout(Duration::from_secs(180)) // Set a timeout to prevent hanging
// //         .build()?;
// //     let mut latest_history_id = String::new(); // Store the latest historyId

// //     loop {
// //         println!("Fetching email list from URL: {}", gmail_api_url);

// //         let response = client
// //             .get(&gmail_api_url)
// //             .bearer_auth(access_token)
// //             .send()
// //             .await;

// //         match response {
// //             Ok(resp) => {
// //                 println!("Received response for email list request");

// //                 let json_resp: Value = match resp.json().await {
// //                     Ok(json) => json,
// //                     Err(e) => {
// //                         eprintln!("Error parsing JSON response: {:?}", e);
// //                         continue; // Skip to the next iteration
// //                     }
// //                 };

// //                 // Save the latest historyId from the response
// //                 if let Some(history_id) = json_resp["historyId"].as_str() {
// //                     latest_history_id = history_id.to_string();
// //                 }

// //                 if let Some(messages) = json_resp["messages"].as_array() {
// //                     for message in messages.iter() {
// //                         if let Some(message_id) = message["id"].as_str() {
// //                             println!("Fetching details for Message ID: {}", message_id);

// //                             let email_response = match client
// //                                 .get(format!("{}/{}", gmail_api_url, message_id))
// //                                 .bearer_auth(access_token)
// //                                 .send()
// //                                 .await
// //                             {
// //                                 Ok(resp) => {
// //                                     if resp.status().is_success() {
// //                                         match resp.json::<Value>().await {
// //                                             Ok(json) => {
// //                                                 // Check if the "id" field is present in the email response
// //                                                 if json.get("id").is_none() {
// //                                                     eprintln!(
// //                                                         "ID not found in email response for Message ID {}: {:?}",
// //                                                         message_id, json
// //                                                     );
// //                                                     continue; // Skip this email if the ID is not found
// //                                                 }
// //                                                 json
// //                                             }
// //                                             Err(e) => {
// //                                                 eprintln!(
// //                                                     "Error parsing email response for Message ID {}: {:?}",
// //                                                     message_id, e
// //                                                 );
// //                                                 continue; // Skip this email
// //                                             }
// //                                         }
// //                                     } else {
// //                                         let error_json: Value =
// //                                             resp.json().await.unwrap_or_default();
// //                                         eprintln!(
// //                                             "API Error Response for Message ID {}: {}",
// //                                             message_id,
// //                                             serde_json::to_string_pretty(&error_json)
// //                                                 .unwrap_or_else(|_| {
// //                                                     "Failed to parse error response".to_string()
// //                                                 })
// //                                         );

// //                                         // Check for "Invalid pageToken" error and reset pagination
// //                                         if let Some(error) = error_json["error"]["message"].as_str() {
// //                                             if error.contains("Invalid pageToken") {
// //                                                 eprintln!("Invalid pageToken detected. Resetting pagination.");
// //                                                 gmail_api_url = "https://www.googleapis.com/gmail/v1/users/me/messages?q=is:unread".to_string();
// //                                                 continue; // Retry fetching without the invalid pageToken
// //                                             }
// //                                         }
// //                                         continue; // Skip to the next message
// //                                     }
// //                                 }
// //                                 Err(e) => {
// //                                     eprintln!(
// //                                         "Request error for Message ID {}: {:?}",
// //                                         message_id, e
// //                                     );
// //                                     continue; // Skip this email and move to the next one
// //                                 }
// //                             };

// //                             // Proceed with parsing the valid email response...
// //                             let email = match parse_email_metadata(
// //                                 &email_response,
// //                                 summarization_model.clone(),
// //                                 sentiment_model.clone(),
// //                                 ner_model.clone(),
// //                             )
// //                             .await
// //                             {
// //                                 Ok(email) => email,
// //                                 Err(e) => {
// //                                     eprintln!(
// //                                         "Error parsing email metadata for Message ID {}: {:?}",
// //                                         message_id, e
// //                                     );
// //                                     continue; // Skip this email
// //                                 }
// //                             };

// //                             println!(
// //                                 "before store_email_metadata - Email sender: \n{:#?}",
// //                                 &email.from
// //                             );
// //                         }
// //                     }
// //                 } else {
// //                     println!("No messages found in the current response");
// //                 }

// //                 // Handle pagination
// //                 if let Some(next_page_token) = json_resp["nextPageToken"].as_str() {
// //                     gmail_api_url = format!(
// //                         "https://www.googleapis.com/gmail/v1/users/me/messages?pageToken={}",
// //                         next_page_token
// //                     );
// //                 } else {
// //                     println!("No more pages to fetch");
// //                     break;
// //                 }
// //             }
// //             Err(e) => {
// //                 eprintln!("Error sending request to Gmail API: {:?}", e);
// //                 tokio::time::sleep(Duration::from_secs(2)).await; // Wait before retrying
// //                 continue; // Retry the request
// //             }
// //         }
// //     }

// //     // Save the latest historyId before exiting the loop
// //     save_history_id(&latest_history_id);

// //     // Return the latest historyId
// //     Ok(latest_history_id)
// // }

// async fn fetch_emails(access_token: &str, url: String) -> Result<String, Box<dyn Error>> {
//     println!(
//         "Let's start fetching emails with access token: {:#?}",
//         access_token
//     );

//     // Initialize the summarization model using spawn_blocking
//     let summarization_model = task::spawn_blocking(|| SummarizationModel::new(Default::default()))
//         .await?
//         .map(|model| Arc::new(Mutex::new(model)))
//         .map_err(|e| {
//             eprintln!("Failed to initialize the summarization model: {:?}", e);
//             Box::new(e) as Box<dyn Error>
//         })?;

//     println!("Summarization model initialized successfully.");

//     // Initialize the sentiment analysis model using spawn_blocking
//     let sentiment_model = task::spawn_blocking(|| SentimentModel::new(Default::default()))
//         .await?
//         .map(|model| Arc::new(Mutex::new(model)))
//         .map_err(|e| {
//             eprintln!("Failed to initialize the sentiment model: {:?}", e);
//             Box::new(e) as Box<dyn Error>
//         })?;

//     println!("Sentiment model initialized successfully.");

//     // Initialize the NER model using spawn_blocking
//     let ner_model = task::spawn_blocking(|| NERModel::new(Default::default()))
//         .await?
//         .map(|model| Arc::new(Mutex::new(model)))
//         .map_err(|e| {
//             eprintln!("Failed to initialize the NER model: {:?}", e);
//             Box::new(e) as Box<dyn Error>
//         })?;

//     let mut blacklisted_domains: HashSet<String> = HashSet::new(); // Initialize with an empty HashSet

//     let mut gmail_api_url = url;
//     let client = Client::builder()
//         .timeout(Duration::from_secs(180)) // Set a timeout to prevent hanging
//         .build()?;
//     let mut latest_history_id = String::new(); // Store the latest historyId

//     loop {
//         println!("Fetching email list from URL: {}", gmail_api_url);

//         let response = client
//             .get(&gmail_api_url)
//             .bearer_auth(access_token)
//             .send()
//             .await;

//         match response {
//             Ok(resp) => {
//                 println!("Received response for email list request");

//                 let json_resp: Value = match resp.json().await {
//                     Ok(json) => json,
//                     Err(e) => {
//                         eprintln!("Error parsing JSON response: {:?}", e);
//                         continue; // Skip to the next iteration
//                     }
//                 };

//                 // Save the latest historyId from the response
//                 if let Some(history_id) = json_resp["historyId"].as_str() {
//                     latest_history_id = history_id.to_string();
//                 }

//                 if let Some(messages) = json_resp["messages"].as_array() {
//                     for message in messages.iter() {
//                         if let Some(message_id) = message["id"].as_str() {
//                             println!("Fetching details for Message ID: {}", message_id);

//                             let email_response = match client
//                                 .get(format!("{}/{}", gmail_api_url, message_id))
//                                 .bearer_auth(access_token)
//                                 .send()
//                                 .await
//                             {
//                                 Ok(resp) => {
//                                     if resp.status().is_success() {
//                                         match resp.json::<Value>().await {
//                                             Ok(json) => {
//                                                 // Log the full response for debugging
//                                                 // println!(
//                                                 //     "Full email response for Message ID {}: {:?}",
//                                                 //     message_id, json
//                                                 // );
//                                                 json
//                                             }
//                                             Err(e) => {
//                                                 eprintln!(
//                                                     "Error parsing email response for Message ID {}: {:?}",
//                                                     message_id, e
//                                                 );
//                                                 continue; // Skip this email
//                                             }
//                                         }
//                                     } else {
//                                         let error_json: Value =
//                                             resp.json().await.unwrap_or_default();
//                                         eprintln!(
//                                             "API Error Response for Message ID {}: {}",
//                                             message_id,
//                                             serde_json::to_string_pretty(&error_json)
//                                                 .unwrap_or_else(|_| {
//                                                     "Failed to parse error response".to_string()
//                                                 })
//                                         );
//                                         if let Some(error) = error_json["error"]["message"].as_str()
//                                         {
//                                             if error.contains("Invalid pageToken") {
//                                                 eprintln!("Invalid pageToken detected. Resetting pagination.");
//                                                 // Reset pagination by starting from the initial URL without the pageToken
//                                                 gmail_api_url = "https://www.googleapis.com/gmail/v1/users/me/messages".to_string();
//                                                 continue; // Restart the loop with the original URL
//                                             }
//                                         }
//                                         continue; // Skip to the next message
//                                     }
//                                 }
//                                 Err(e) => {
//                                     eprintln!(
//                                         "Request error for Message ID {}: {:?}",
//                                         message_id, e
//                                     );
//                                     continue; // Skip this email and move to the next one
//                                 }
//                             };

//                             // Check if the "id" field is present in the email response
//                             if email_response.get("id").is_none() {
//                                 eprintln!(
//                                     "ID not found in email response for Message ID {}: {:?}",
//                                     message_id, email_response
//                                 );
//                                 continue; // Skip this email if the ID is not found
//                             }

//                             // Proceed with parsing the valid email response...
//                             let email = match parse_email_metadata(
//                                 &email_response,
//                                 summarization_model.clone(),
//                                 sentiment_model.clone(),
//                                 ner_model.clone(), // &blacklisted_domains,
//                             )
//                             .await
//                             {
//                                 Ok(email) => email,
//                                 Err(e) => {
//                                     eprintln!(
//                                         "Error parsing email metadata for Message ID {}: {:?}",
//                                         message_id, e
//                                     );
//                                     continue; // Skip this email
//                                 }
//                             };

//                             println!(
//                                 "before store_email_metadata - Email sender: \n{:#?}",
//                                 &email.from
//                             );

//                             // emails.push(email);

//                             // if emails.len() > 100 {
//                             //     store_and_reset_emails(&mut emails);
//                             //     emails.clear();
//                             // }
//                         } else {
//                             eprintln!("Error: message['id'] is None");
//                         }
//                     }
//                 } else {
//                     println!("No messages found in the current response");
//                 }

//                 // Handle pagination
//                 if let Some(next_page_token) = json_resp["nextPageToken"].as_str() {
//                     gmail_api_url = format!(
//                         "https://www.googleapis.com/gmail/v1/users/me/messages?pageToken={}",
//                         next_page_token
//                     );
//                 } else {
//                     println!("No more pages to fetch");
//                     break;
//                 }
//             }
//             Err(e) => {
//                 eprintln!("Error sending request to Gmail API: {:?}", e);
//                 // Implement a retry mechanism with exponential backoff
//                 tokio::time::sleep(Duration::from_secs(2)).await; // Wait before retrying
//                 continue; // Retry the request
//             }
//         }
//     }
//  // Save the latest historyId before exiting the loop
//  save_history_id(&latest_history_id);

//  // Return the latest historyId
//  Ok(latest_history_id)
// }

// // async fn fetch_emails(access_token: &str, url: String) -> Result<String, Box<dyn Error>> {
// //     println!("Let's start fetching emails with access token: {:#?}", access_token);

// //     // Moved model initialization outside the loop to avoid repeated initializations
// //     let summarization_model = task::spawn_blocking(|| SummarizationModel::new(Default::default()))
// //         .await?
// //         .map(|model| Arc::new(Mutex::new(model)))
// //         .map_err(|e| {
// //             eprintln!("Failed to initialize the summarization model: {:?}", e);
// //             Box::new(e) as Box<dyn Error>
// //         })?;

// //     let sentiment_model = task::spawn_blocking(|| SentimentModel::new(Default::default()))
// //         .await?
// //         .map(|model| Arc::new(Mutex::new(model)))
// //         .map_err(|e| {
// //             eprintln!("Failed to initialize the sentiment model: {:?}", e);
// //             Box::new(e) as Box<dyn Error>
// //         })?;

// //     let ner_model = task::spawn_blocking(|| NERModel::new(Default::default()))
// //         .await?
// //         .map(|model| Arc::new(Mutex::new(model)))
// //         .map_err(|e| {
// //             eprintln!("Failed to initialize the NER model: {:?}", e);
// //             Box::new(e) as Box<dyn Error>
// //         })?;

// //     let mut gmail_api_url = url;
// //     let client = Client::builder()
// //         .timeout(Duration::from_secs(180)) // Set a timeout
// //         .build()?;
// //     let mut latest_history_id = String::new(); // Store the latest historyId

// //     // Implement batching for processing multiple email IDs at once
// //     const BATCH_SIZE: usize = 50; // Number of email details to fetch in one batch

// //     let mut next_page_token: Option<String> = None;
// //     let mut retries = 0;
// //     let max_retries = 5;

// //     loop {
// //         // Implement back-off strategy in case of API rate limiting
// //         let response = client
// //             .get(&gmail_api_url)
// //             .bearer_auth(access_token)
// //             .send()
// //             .await;

// //         match response {
// //             Ok(resp) => {
// //                 let json_resp: Value = match resp.json().await {
// //                     Ok(json) => json,
// //                     Err(e) => {
// //                         eprintln!("Error parsing JSON response: {:?}", e);
// //                         if retries < max_retries {
// //                             retries += 1;
// //                             let backoff_time = 2_u64.pow(retries);
// //                             tokio::time::sleep(Duration::from_secs(backoff_time)).await;
// //                             continue;
// //                         } else {
// //                             return Err(Box::new(e));
// //                         }
// //                     }
// //                 };

// //                 if let Some(history_id) = json_resp["historyId"].as_str() {
// //                     latest_history_id = history_id.to_string();
// //                 }

// //                 if let Some(messages) = json_resp["messages"].as_array() {
// //                     let message_ids: Vec<String> = messages.iter()
// //                         .filter_map(|msg| msg["id"].as_str().map(|id| id.to_string()))
// //                         .collect();

// //                     // Process messages in batches
// //                     for batch in message_ids.chunks(BATCH_SIZE) {
// //                         let mut batch_futures = vec![];

// //                         for message_id in batch {
// //                             let client_clone = client.clone();
// //                             let token = access_token.to_string();
// //                             let url = format!("{}/{}", gmail_api_url, message_id);

// //                             batch_futures.push(tokio::spawn(async move {
// //                                 let response = client_clone
// //                                     .get(&url)
// //                                     .bearer_auth(token)
// //                                     .send()
// //                                     .await;

// //                                 match response {
// //                                     Ok(resp) => match resp.json::<Value>().await {
// //                                         Ok(json) => Ok(json),
// //                                         Err(e) => Err(e),
// //                                     },
// //                                     Err(e) => Err(e),
// //                                 }
// //                             }));
// //                         }

// //                         // Wait for all email details in the batch to complete
// //                         let results = futures::future::join_all(batch_futures).await;

// //                         // Handle each email detail result
// //                         for result in results {
// //                             match result {
// //                                 Ok(Ok(email_response)) => {
// //                                     if email_response.get("id").is_none() {
// //                                         eprintln!("ID not found in email response: {:?}", email_response);
// //                                         continue;
// //                                     }

// //                                     // Use the models for further processing
// //                                     let email = parse_email_metadata(
// //                                         &email_response,
// //                                         summarization_model.clone(),
// //                                         sentiment_model.clone(),
// //                                         ner_model.clone(),
// //                                     ).await;

// //                                     // Log email data for debugging
// //                                     // match email {
// //                                     //     Ok(email_data) => println!("Parsed email: {:?}", email_data),
// //                                     //     Err(e) => eprintln!("Error parsing email: {:?}", e),
// //                                     // }
// //                                 }
// //                                 Ok(Err(e)) => {
// //                                     eprintln!("Error fetching email details: {:?}", e);
// //                                 }
// //                                 Err(e) => {
// //                                     eprintln!("Join error: {:?}", e);
// //                                 }
// //                             }
// //                         }
// //                     }
// //                 } else {
// //                     println!("No messages found in the current response.");
// //                     break;
// //                 }

// //                 // Pagination handling
// //                 next_page_token = json_resp["nextPageToken"].as_str().map(|s| s.to_string());
// //                 if let Some(token) = next_page_token {
// //                     gmail_api_url = format!(
// //                         "https://www.googleapis.com/gmail/v1/users/me/messages?pageToken={}",
// //                         token
// //                     );
// //                 } else {
// //                     break;
// //                 }
// //             }
// //             Err(e) => {
// //                 eprintln!("Error sending request to Gmail API: {:?}", e);
// //                 if retries < max_retries {
// //                     retries += 1;
// //                     let backoff_time = 2_u64.pow(retries);
// //                     tokio::time::sleep(Duration::from_secs(backoff_time)).await;
// //                     continue;
// //                 } else {
// //                     return Err(Box::new(e));
// //                 }
// //             }
// //         }
// //     }

// //     // Save the latest historyId before exiting the loop
// //     save_history_id(&latest_history_id);

// //     Ok(latest_history_id)
// // }

// Save the historyId to a file
// fn save_history_id(history_id: &str) {
//     std::fs::write("history_id.txt", history_id).expect("Unable to save history_id");
// }

// // Function to store emails and reset the list
// fn store_and_reset_emails(emails: &mut Vec<Email>) {
//     for email in emails.iter() {
//         store_email_metadata(email);
//     }
//     // Clear the emails list after storing
// }

// // Function to store email metadata
// fn store_email_metadata(email: &Email) -> Result<(), Box<dyn std::error::Error>> {
//     // Insert the email metadata into the database
//     emails_service::store_new_email(&email);

//     Ok(())
// }

// fn extract_name_company_domain(email: &str) -> (String, String, String) {
//     println!(
//         "Inside extract_name_company_domain and extract company name \n {:#?}",
//         email
//     );

//     // Regex to match the name before the email address and the domain within the email address
//     let re =
//         Regex::new(r"^(.*?)(?:\s*<)?([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(?:>)?$")
//             .unwrap();

//     if let Some(caps) = re.captures(email) {
//         let name = caps.get(1).map_or("", |m| m.as_str()).trim().to_string();
//         let domain = caps.get(3).map_or("", |m| m.as_str()).to_string();
//         let company = domain.split('.').next().unwrap_or("").to_string();
//         return (name, company, domain);
//     }

//     (
//         "Unknown".to_string(),
//         "Unknown".to_string(),
//         "unknown.com".to_string(),
//     )
// }

fn activate_virtual_env(venv_path: &str) -> Result<Output, Box<dyn Error>> {
    let activate_script = format!("{}/bin/activate", venv_path);

    let shell_command = format!("source {} && echo $VIRTUAL_ENV", activate_script);

    let output = Command::new("sh").arg("-c").arg(shell_command).output()?;

    if !output.stdout.is_empty() {
        let virtual_env_path = String::from_utf8_lossy(&output.stdout).trim().to_string();
        let virtual_env_bin = format!("{}/bin", virtual_env_path);

        env::set_var(
            "PATH",
            format!("{}:{}", virtual_env_bin, env::var("PATH").unwrap_or_default())
        );

        Ok(output)
    } else {
        Err("Failed to activate virtual environment".into())
    }
}

fn print_email_response(email_response: &Value) {
    let pretty_json = serde_json
        ::to_string_pretty(&email_response)
        .unwrap_or_else(|_| "Error converting to JSON".to_string());
    println!("Email Response: {}", pretty_json);
}

fn extract_name(from: &str) -> String {
    // Split by '<' and take the first part
    let name_part = from.split('<').next().unwrap_or("").trim();

    // Remove surrounding quotes if present
    let name = if name_part.starts_with('"') && name_part.ends_with('"') {
        name_part.trim_matches('"').to_string()
    } else {
        name_part.to_string()
    };

    name
}

// async fn parse_email_metadata(
//     email_response: &Value,
//     summarization_model: Arc<Mutex<SummarizationModel>>,
//     sentiment_model: Arc<Mutex<SentimentModel>>,
//     ner_model: Arc<Mutex<NERModel>>,
//     // blacklisted_domains: &HashSet<String>, // Add blacklisted_domains parameter
// ) -> Result<Email, Box<dyn Error>> {
//     // println!("Email Response: {}", email_response);
//     let id = email_response["id"]
//         .as_str()
//         .ok_or("ID not found in email response")?
//         .to_string();

//     let headers = email_response["payload"]["headers"]
//         .as_array()
//         .ok_or("Headers not found in email response")?;

//     let mut subject = String::new();
//     let mut from = String::new();
//     let mut to = String::new();
//     let mut cc = String::new();
//     let mut bcc = String::new();
//     let mut date = NaiveDateTime::from_timestamp_opt(0, 0).unwrap();

//     for header in headers {
//         let header_name = header["name"].as_str().unwrap_or("Unknown");
//         let header_value = header["value"].as_str().unwrap_or("Unknown");

//         match header_name {
//             "Subject" => subject = header_value.to_string(),
//             "From" => from = header_value.to_string(),
//             "To" => to = header_value.to_string(),
//             "Cc" => cc = header_value.to_string(),
//             "Bcc" => bcc = header_value.to_string(),
//             "Date" => {
//                 if let Ok(parsed_date) = chrono::DateTime::parse_from_rfc2822(header_value) {
//                     date = parsed_date.naive_utc();
//                 }
//             }
//             _ => {}
//         }
//     }

//     if from.is_empty() {
//         return Err("From not found in email response".into());
//     }

//     let sender_domain = from.split('@').nth(1).unwrap_or("").to_string();

//     let email_data = json!({
//         "labelIds": email_response["labelIds"].as_array()
//                       .map(|arr| arr.iter().map(|v| v.to_string()).collect::<Vec<String>>())
//                       .unwrap_or_else(Vec::new), // Default to an empty array of strings if null
//         "snippet": email_response["snippet"].as_str().unwrap_or("").to_lowercase(), // Default to an empty string if null
//         "subject": subject.to_lowercase(),
//         "sender_domain": sender_domain,
//         "threadId": email_response["threadId"].as_str().unwrap_or(""), // Default to an empty string if null
//         "attachments": email_response["payload"]["parts"].as_array()
//                         .map(|arr| arr.iter().map(|v| v.to_string()).collect::<Vec<String>>())
//                         .unwrap_or_else(Vec::new), // Default to an empty array of strings if null
//     });

//     let email_data_hashmap = match convert_json_to_hashmap_async(&email_data).await {
//         Ok(map) => map,
//         Err(e) => {
//             eprintln!("Failed to convert JSON to HashMap: {}", e);
//             return Err(Box::new(std::io::Error::new(std::io::ErrorKind::Other, e)));
//             // Return an error
//         }
//     };
//     // Pass the model to categorize_email_from_metadata
//     let email_info: HashMap<String, Option<String>> = categorize_email_from_metadata(
//         email_data_hashmap,
//         summarization_model.clone(),
//         sentiment_model.clone(),
//         ner_model.clone(),
//     )
//     .await;

//     let main_domain = extract_main_domain(
//         &from
//             .split('@')
//             .nth(1)
//             .unwrap_or("")
//             .trim_end_matches('>')
//             .to_string(),
//     );
//     println!("Extracted main domain: {}", main_domain);
//     let full_domain = extract_full_domain(
//         &from
//             .split('@')
//             .nth(1)
//             .unwrap_or("")
//             .trim_end_matches('>')
//             .to_string(),
//     );
//     // Assign child_domain: if full_domain equals main_domain, assign empty string, otherwise assign full_domain
//     let child_domain = if Some(full_domain.clone()) == Some(main_domain.clone()) {
//         // "".to_string() // Empty string if full_domain equals main_domain
//         full_domain.clone() // Full domain if they are not equal
//     } else {
//         full_domain.clone() // Full domain if they are not equal
//     };
//     // Create or update the email category based on the email's domain and other info
//     email_category_commands::new_email_category(
//         full_domain.clone(), // Provide a default empty string if None
//         child_domain,
//         extract_name(&from.clone()),
//         extract_name(&from.clone()),
//         main_domain.clone(),
//         from.clone(),
//         email_info
//             .get("tags")
//             .and_then(|v| v.as_deref())
//             .unwrap_or("")
//             .to_string(),
//         Some(
//             email_info
//                 .get("description")
//                 .and_then(|v| v.as_deref())
//                 .unwrap_or("")
//                 .to_string(),
//         ),
//         Some(
//             email_info
//                 .get("priority")
//                 .and_then(|v| v.as_deref().and_then(|s| s.parse::<i64>().ok()))
//                 .unwrap_or(0) as i32,
//         ),
//         Some(
//             email_info
//                 .get("color")
//                 .and_then(|v| v.as_deref())
//                 .unwrap_or("")
//                 .to_string(),
//         ),
//         Some(
//             email_info
//                 .get("unread_count")
//                 .and_then(|v| v.as_deref().and_then(|s| s.parse::<i64>().ok()))
//                 .unwrap_or(0) as i32,
//         ),
//         email_info
//             .get("visibility")
//             .and_then(|v| v.as_deref())
//             .unwrap_or("")
//             .to_string(),
//         Some(chrono::Utc::now().naive_utc().to_string()),
//         Some(false),
//     );
//     let email = Email {
//         id: id.clone(),
//         subject: subject.clone(),
//         snippet: email_info
//             .get("snippet")
//             .and_then(|v| v.as_deref())
//             .unwrap_or("")
//             .to_string(),
//         from: from.clone(),
//         to: to.clone(),
//         cc: cc.clone(),
//         bcc: bcc.clone(),
//         date: date.clone(),
//         category: from
//             .split('@')
//             .nth(1)
//             .unwrap_or("")
//             .trim_end_matches('>')
//             .to_string(),
//         labels: email_info
//             .get("labels")
//             .and_then(|v| v.as_deref())
//             .unwrap_or("")
//             .to_string(),
//         attachments: email_info
//             .get("attachments")
//             .and_then(|v| v.as_deref())
//             .unwrap_or("")
//             .to_string(),
//         metadata_headers: serde_json::to_string(&headers)?,
//         email_body_url: email_info
//             .get("email_body_url")
//             .and_then(|v| v.as_deref())
//             .map(|s| s.to_string()),
//         is_read: Some(
//             email_info
//                 .get("is_read")
//                 .and_then(|v| v.as_deref().and_then(|s| s.parse::<bool>().ok()))
//                 .unwrap_or(false),
//         ),
//         thread_id: email_info
//             .get("thread_id")
//             .and_then(|v| v.as_deref())
//             .map(|s| s.to_string()),
//         importance: email_info
//             .get("importance")
//             .and_then(|v| v.as_deref().and_then(|s| s.parse::<i64>().ok()))
//             .map(|v| v as i32),
//         storage_location: email_info
//             .get("storage_location")
//             .and_then(|v| v.as_deref())
//             .map(|s| s.to_string()),
//         custom_headers: email_info
//             .get("custom_headers")
//             .and_then(|v| v.as_deref())
//             .map(|s| s.to_string()),
//         is_flagged: Some(
//             email_info
//                 .get("is_flagged")
//                 .and_then(|v| v.as_deref().and_then(|s| s.parse::<bool>().ok()))
//                 .unwrap_or(false),
//         ),
//         urgency_score: email_info
//             .get("urgency_score")
//             .and_then(|v| v.as_deref().and_then(|s| s.parse::<i64>().ok()))
//             .map(|v| v as i32),
//         sentiment: email_info
//             .get("sentiment")
//             .and_then(|v| v.as_deref())
//             .map(|s| s.to_string()),
//         actionable_items: email_info
//             .get("actionable_items")
//             .and_then(|v| v.as_deref())
//             .map(|arr| {
//                 let items: Vec<String> = serde_json::from_str(arr).unwrap_or_default();
//                 serde_json::to_string(&items).unwrap_or_default()
//             }),
//         language: email_info
//             .get("language")
//             .and_then(|v| v.as_deref())
//             .map(|s| s.to_string()),
//         phishing_risk: email_info
//             .get("phishing_risk")
//             .and_then(|v| v.as_deref())
//             .map(|s| s.to_string()),
//         sender_reputation: email_info
//             .get("sender_reputation")
//             .and_then(|v| v.as_deref())
//             .map(|s| s.to_string()),

//         // Newly added fields:
//         /*
//         DROP TABLE IF EXISTS __diesel_schema_migrations;
//         DROP TABLE IF EXISTS meetings;
//         DROP TABLE IF EXISTS assistants;
//         DROP TABLE IF EXISTS messages;
//         DROP TABLE IF EXISTS email_categories;
//         DROP TABLE IF EXISTS sessions;
//         DROP TABLE IF EXISTS emails;
//         DROP TABLE IF EXISTS email_logs;

//                  */
//         attachment_types: email_info
//             .get("attachment_types")
//             .and_then(|v| v.as_deref())
//             .map(|s| s.to_string()),
//         total_attachment_size: email_info
//             .get("total_attachment_size")
//             .and_then(|v| v.as_deref().and_then(|s| s.parse::<i64>().ok()))
//             .map(|v| v as i32),
//         thread_summary: email_info
//             .get("thread_summary")
//             .and_then(|v| v.as_deref())
//             .map(|s| s.to_string()),
//         bucket: email_info
//             .get("bucket")
//             .and_then(|v| v.as_deref())
//             .map(|s| s.to_string()),
//         priority: email_info
//             .get("priority")
//             .and_then(|v| v.as_deref())
//             .map(|s| s.to_string()),
//         main_domain: Some(main_domain),
//         full_domain: Some(full_domain),
//     };
//     emails_service::store_new_email(&email);
//     Ok(email)
// }

// // Helper function to extract main domain
// fn extract_main_domain(email_or_domain: &str) -> String {
//     let parts: Vec<&str> = email_or_domain.split('@').collect();
//     let domain = if parts.len() > 1 {
//         parts[1]
//     } else {
//         email_or_domain
//     };
//     let domain_parts: Vec<&str> = domain.split('.').collect();

//     if domain_parts.len() >= 2 {
//         let main_domain =
//             domain_parts[domain_parts.len() - 2].to_string() + "." + domain_parts.last().unwrap();
//         main_domain
//     } else {
//         domain.to_string()
//     }
// }

// // Helper function to extract full domain (subdomain.domain.com)
// fn extract_full_domain(email_or_domain: &str) -> String {
//     let parts: Vec<&str> = email_or_domain.split('@').collect();
//     if parts.len() > 1 {
//         parts[1].to_string() // Return full domain part
//     } else {
//         email_or_domain.to_string() // It's already a domain
//     }
// }

// async fn convert_json_to_hashmap_async(json: &Value) -> Result<HashMap<String, String>, String> {
//     let mut map = HashMap::new();

//     if let Some(obj) = json.as_object() {
//         for (key, value) in obj {
//             if value.is_null() {
//                 // println!("Skipping key: {} because the value is null", key);
//                 map.insert(key.clone(), String::new());
//             } else if let Some(value_str) = value.as_str() {
//                 // println!("Inserting key: {} with value: {}", key, value_str);
//                 map.insert(key.clone(), value_str.to_string());
//             } else {
//                 // println!("Inserting key: {} with value: {}", key, value.to_string());
//                 map.insert(key.clone(), value.to_string());
//             }
//         }
//         Ok(map)
//     } else {
//         let error_message = format!("Error: Provided JSON is not an object. JSON: {:?}", json);
//         Err(error_message)
//     }
// }

// fn assign_priority(subject: &str, sender_domain: &str) -> PyResult<(i32, String)> {
//     let script_path = "python/email_priority.py";

//     let py_code = fs::read_to_string(script_path).expect("Failed to read Python script file");

//     Python::with_gil(|py| {
//         let py_globals = [("re", py.import("re")?)].into_py_dict(py);
//         py.run(&py_code, Some(py_globals), None)?;

//         let func = py_globals.get_item("assign_email_priority")
//             .ok_or_else(|| PyErr::new::<pyo3::exceptions::PyAttributeError, _>("Function not found"))?;

//         // Create a Python tuple with the subject and sender_domain
//         let args = PyTuple::new(py, &[subject, sender_domain]);

//         // Call the Python function with the tuple of arguments
//         let result: (i32, String) = func.call1(args)?.extract()?;
//         Ok(result)
//     })
// }
