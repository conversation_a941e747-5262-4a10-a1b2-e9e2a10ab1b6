import { invoke } from '@tauri-apps/api/core'

export interface EmailLog {
  id: string
  email_id: string
  snoozed_until: string
  created_at: string
}

export interface SnoozeEmailParams {
  emailId: string
  snoozedUntil: string
}

// Define the categories that support snoozing
export const SNOOZABLE_CATEGORIES = [
  'needs_reply',
  'waiting_response',
  'fyi_read_later',
  'delegated_handled',
  'calendar_scheduling',
  'clients_vips',
  'ads_newsletters'
] as const

export type SnoozableCategory = typeof SNOOZABLE_CATEGORIES[number]

/**
 * Check if an email category supports snoozing
 * @param category - The email category to check
 * @returns boolean - True if the category can be snoozed
 */
export function canSnoozeCategory(category: string | null | undefined): boolean {
  if (!category) return false
  return SNOOZABLE_CATEGORIES.includes(category as SnoozableCategory)
}

/**
 * Snooze an email until a specific date and time
 * @param emailId - The ID of the email to snooze
 * @param snoozeUntil - The date and time when the email should resurface
 * @param emailCategory - Optional email category for validation
 * @returns Promise<EmailLog> - The created email log entry
 * @throws Error if the email category doesn't support snoozing
 */
export async function snoozeEmail(
  emailId: string,
  snoozeUntil: Date,
  emailCategory?: string
): Promise<EmailLog> {
  // Validate that the email category supports snoozing
  if (emailCategory && !canSnoozeCategory(emailCategory)) {
    throw new Error(`Email category "${emailCategory}" does not support snoozing. Only these categories can be snoozed: ${SNOOZABLE_CATEGORIES.join(', ')}`)
  }

  // Format the date for the Rust backend (YYYY-MM-DDTHH:MM:SS)
  const formattedDate = snoozeUntil.toISOString().slice(0, 19)

  return await invoke<EmailLog>('add_email_snooze', {
    emailId,
    snoozedUntil: formattedDate
  })
}

/**
 * Get all currently snoozed emails
 * @returns Promise<EmailLog[]> - Array of active snoozed emails
 */
export async function getActiveSnoozedEmails(): Promise<EmailLog[]> {
  return await invoke<EmailLog[]>('get_active_snoozed_emails')
}

/**
 * Manually trigger resurfacing of due emails
 * @returns Promise<number> - Number of emails that were resurfaced
 */
export async function resurfaceDueEmails(): Promise<number> {
  return await invoke<number>('resurface_due_emails')
}

/**
 * Helper function to create common snooze durations
 */
export const SnoozePresets = {
  oneHour: () => new Date(Date.now() + 60 * 60 * 1000),
  fiveHours: () => new Date(Date.now() + 5 * 60 * 60 * 1000),
  oneDay: () => new Date(Date.now() + 24 * 60 * 60 * 1000),
  threeDays: () => new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
  oneWeek: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),

  /**
   * Create a custom snooze time
   * @param hours - Number of hours to snooze
   * @returns Date object for the snooze time
   */
  custom: (hours: number) => new Date(Date.now() + hours * 60 * 60 * 1000)
}
