<template>
  <div class="flex size-full items-center justify-center text-slate-500">
    <div class="text-xs font-semibold flex-1">{{ props.name }}</div>
    <div class="group flex-1 flex justify-end items-center gap-2">
      <Input
        :value="props.value"
        :type="props.type"
        :editing="props.editing"
        @on-update="(attr, value) => emit('onUpdate', attr, value)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import Input from "../components/input/input.vue";

const props = defineProps({
  name: String,
  value: String,
  editing: {
    type: Boolean,
    default: false,
  },
  attr: String,
  type: {
    type: String,
    default: "text",
  },
});

const emit = defineEmits<{
  (e: "onUpdate", attr: string, value: string): void;
}>();
</script>

<style scoped></style>
