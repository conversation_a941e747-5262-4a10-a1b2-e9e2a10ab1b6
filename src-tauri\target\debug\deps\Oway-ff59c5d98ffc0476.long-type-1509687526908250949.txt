SelectStatement<FromClause<schema::email_logs::table>, diesel::query_builder::select_clause::SelectClause<schema::email_logs::columns::email_id>, diesel::query_builder::distinct_clause::NoDistinctClause, diesel::query_builder::where_clause::WhereClause<expression::grouped::Grouped<expression::operators::And<expression::grouped::Grouped<expression::operators::Gt<schema::email_logs::columns::snoozed_until, expression::bound::Bound<diesel::sql_types::Timestamp, NaiveDateTime>>>, expression::grouped::Grouped<expression::operators::Or<expression::grouped::Grouped<expression::operators::Eq<schema::email_logs::columns::resurfaced, expression::bound::Bound<diesel::sql_types::Nullable<diesel::sql_types::Bool>, bool>>>, expression::grouped::Grouped<expression::operators::IsNull<schema::email_logs::columns::resurfaced>>>>>>>>
SelectStatement<FromClause<schema::email_logs::table>, diesel::query_builder::select_clause::SelectClause<schema::email_logs::columns::email_id>, diesel::query_builder::distinct_clause::NoDistinctClause, diesel::query_builder::where_clause::WhereClause<expression::grouped::Grouped<expression::operators::And<expression::grouped::Grouped<expression::operators::Gt<schema::email_logs::columns::snoozed_until, expression::bound::Bound<diesel::sql_types::Timestamp, NaiveDateTime>>>, expression::grouped::Grouped<expression::operators::Or<expression::grouped::Grouped<expression::operators::Eq<schema::email_logs::columns::resurfaced, expression::bound::Bound<diesel::sql_types::Nullable<diesel::sql_types::Bool>, bool>>>, expression::grouped::Grouped<expression::operators::IsNull<schema::email_logs::columns::resurfaced>>>>>>>>: AsInExpression<diesel::sql_types::Nullable<diesel::sql_types::Text>>
